#!/bin/bash

# DISC Traits 部署脚本
# 用于自动化构建和部署流程

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查必要的工具
check_dependencies() {
    log_info "检查依赖工具..."
    
    if ! command -v node &> /dev/null; then
        log_error "Node.js 未安装"
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        log_error "npm 未安装"
        exit 1
    fi
    
    if ! command -v git &> /dev/null; then
        log_error "Git 未安装"
        exit 1
    fi
    
    log_success "所有依赖工具已安装"
}

# 检查环境变量
check_environment() {
    log_info "检查环境配置..."
    
    if [ ! -f ".env.local" ] && [ ! -f ".env.production" ]; then
        log_warning "未找到环境配置文件，将使用默认配置"
    fi
    
    # 检查必要的环境变量
    if [ -z "$VITE_GA_MEASUREMENT_ID" ]; then
        log_warning "未设置 Google Analytics ID"
    fi
    
    log_success "环境配置检查完成"
}

# 清理旧文件
clean_build() {
    log_info "清理构建文件..."
    
    if [ -d "dist" ]; then
        rm -rf dist
        log_success "已清理 dist 目录"
    fi
    
    if [ -d "node_modules/.vite" ]; then
        rm -rf node_modules/.vite
        log_success "已清理 Vite 缓存"
    fi
}

# 安装依赖
install_dependencies() {
    log_info "安装项目依赖..."
    
    npm ci --silent
    
    log_success "依赖安装完成"
}

# 运行测试
run_tests() {
    log_info "运行测试..."
    
    # 类型检查
    if command -v tsc &> /dev/null; then
        npm run type-check
        log_success "TypeScript 类型检查通过"
    fi
    
    # ESLint 检查
    if [ -f ".eslintrc.js" ] || [ -f ".eslintrc.json" ]; then
        npm run lint
        log_success "ESLint 检查通过"
    fi
    
    # 单元测试（如果存在）
    if [ -f "vitest.config.ts" ] || [ -f "jest.config.js" ]; then
        npm run test
        log_success "单元测试通过"
    fi
}

# 构建项目
build_project() {
    log_info "构建生产版本..."
    
    # 设置生产环境
    export NODE_ENV=production
    
    # 构建
    npm run build
    
    # 检查构建结果
    if [ ! -d "dist" ]; then
        log_error "构建失败：未生成 dist 目录"
        exit 1
    fi
    
    # 检查关键文件
    if [ ! -f "dist/index.html" ]; then
        log_error "构建失败：未生成 index.html"
        exit 1
    fi
    
    log_success "项目构建完成"
}

# 优化构建结果
optimize_build() {
    log_info "优化构建结果..."
    
    # 压缩 HTML
    if command -v html-minifier &> /dev/null; then
        find dist -name "*.html" -exec html-minifier --collapse-whitespace --remove-comments --minify-css --minify-js {} \;
        log_success "HTML 文件已压缩"
    fi
    
    # 生成 gzip 文件
    if command -v gzip &> /dev/null; then
        find dist -type f \( -name "*.js" -o -name "*.css" -o -name "*.html" \) -exec gzip -k {} \;
        log_success "已生成 gzip 压缩文件"
    fi
    
    # 显示构建大小
    if command -v du &> /dev/null; then
        BUILD_SIZE=$(du -sh dist | cut -f1)
        log_info "构建大小: $BUILD_SIZE"
    fi
}

# 验证构建
validate_build() {
    log_info "验证构建结果..."
    
    # 检查关键文件
    REQUIRED_FILES=(
        "dist/index.html"
        "dist/manifest.json"
        "dist/robots.txt"
        "dist/sitemap.xml"
        "dist/sw.js"
        "dist/offline.html"
    )
    
    for file in "${REQUIRED_FILES[@]}"; do
        if [ ! -f "$file" ]; then
            log_warning "缺少文件: $file"
        fi
    done
    
    # 检查资源文件
    if [ ! -d "dist/assets" ]; then
        log_warning "缺少 assets 目录"
    fi
    
    log_success "构建验证完成"
}

# 部署到服务器
deploy_to_server() {
    local DEPLOY_TARGET=$1
    
    log_info "部署到 $DEPLOY_TARGET..."
    
    case $DEPLOY_TARGET in
        "staging")
            # 部署到测试环境
            log_info "部署到测试环境..."
            # rsync -avz --delete dist/ user@staging-server:/var/www/disc-traits/
            ;;
        "production")
            # 部署到生产环境
            log_info "部署到生产环境..."
            # rsync -avz --delete dist/ user@prod-server:/var/www/disc-traits/
            ;;
        "cdn")
            # 部署到CDN
            log_info "部署到CDN..."
            # aws s3 sync dist/ s3://disc-traits-cdn/ --delete
            ;;
        *)
            log_error "未知的部署目标: $DEPLOY_TARGET"
            exit 1
            ;;
    esac
    
    log_success "部署完成"
}

# 生成部署报告
generate_report() {
    log_info "生成部署报告..."
    
    REPORT_FILE="deploy-report-$(date +%Y%m%d-%H%M%S).txt"
    
    cat > "$REPORT_FILE" << EOF
DISC Traits 部署报告
==================

部署时间: $(date)
Git 提交: $(git rev-parse HEAD)
Git 分支: $(git branch --show-current)
Node 版本: $(node --version)
npm 版本: $(npm --version)

构建信息:
- 构建大小: $(du -sh dist 2>/dev/null | cut -f1 || echo "未知")
- 文件数量: $(find dist -type f | wc -l)

部署状态: 成功
EOF
    
    log_success "部署报告已生成: $REPORT_FILE"
}

# 主函数
main() {
    local DEPLOY_TARGET=${1:-"staging"}
    
    log_info "开始部署 DISC Traits..."
    log_info "部署目标: $DEPLOY_TARGET"
    
    # 执行部署步骤
    check_dependencies
    check_environment
    clean_build
    install_dependencies
    
    # 可选的测试步骤
    if [ "$SKIP_TESTS" != "true" ]; then
        run_tests
    fi
    
    build_project
    optimize_build
    validate_build
    
    # 部署
    if [ "$DEPLOY_TARGET" != "local" ]; then
        deploy_to_server "$DEPLOY_TARGET"
    fi
    
    generate_report
    
    log_success "部署完成！"
}

# 显示帮助信息
show_help() {
    cat << EOF
DISC Traits 部署脚本

用法:
    $0 [选项] [部署目标]

部署目标:
    local       仅构建，不部署 (默认)
    staging     部署到测试环境
    production  部署到生产环境
    cdn         部署到CDN

选项:
    -h, --help      显示此帮助信息
    --skip-tests    跳过测试步骤
    --clean-only    仅清理构建文件

环境变量:
    SKIP_TESTS=true     跳过测试
    NODE_ENV=production 生产环境构建

示例:
    $0                  # 本地构建
    $0 staging          # 部署到测试环境
    $0 production       # 部署到生产环境
    SKIP_TESTS=true $0  # 跳过测试的构建

EOF
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        --skip-tests)
            export SKIP_TESTS=true
            shift
            ;;
        --clean-only)
            clean_build
            exit 0
            ;;
        *)
            DEPLOY_TARGET=$1
            shift
            ;;
    esac
done

# 运行主函数
main "${DEPLOY_TARGET:-local}"
