# DISC Traits 环境变量配置示例
# 复制此文件为 .env.local 并填入实际值

# 应用基本配置
VITE_APP_NAME=DISC Traits
VITE_APP_VERSION=1.0.0
VITE_APP_DESCRIPTION=Free DISC personality assessment tool
VITE_APP_URL=https://disctraits.com

# Google Analytics 配置
VITE_GA_MEASUREMENT_ID=G-XXXXXXXXXX

# API 配置
VITE_API_BASE_URL=https://api.disctraits.com
VITE_API_VERSION=v1

# 功能开关
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_ERROR_REPORTING=true
VITE_ENABLE_PERFORMANCE_MONITORING=true
VITE_ENABLE_A11Y_FEATURES=true
VITE_ENABLE_PWA=true

# 安全配置
VITE_ENABLE_CSP=true
VITE_ENABLE_SECURITY_HEADERS=true

# 开发配置
VITE_DEV_DASHBOARD=true
VITE_DEBUG_MODE=false

# 第三方服务
VITE_SENTRY_DSN=https://<EMAIL>/project-id
VITE_HOTJAR_ID=your-hotjar-id

# 邮件服务
VITE_CONTACT_EMAIL=<EMAIL>
VITE_FEEDBACK_EMAIL=<EMAIL>

# 社交媒体
VITE_SOCIAL_TWITTER=https://twitter.com/disctraits
VITE_SOCIAL_LINKEDIN=https://linkedin.com/company/disc-traits
VITE_SOCIAL_FACEBOOK=https://facebook.com/disctraits

# CDN 配置
VITE_CDN_URL=https://cdn.disctraits.com
VITE_ASSETS_URL=https://assets.disctraits.com

# 缓存配置
VITE_CACHE_VERSION=1.0.0
VITE_SW_VERSION=1.0.0

# 多语言配置
VITE_DEFAULT_LANGUAGE=zh
VITE_SUPPORTED_LANGUAGES=zh,en,es

# 评估配置
VITE_ASSESSMENT_TIMEOUT=1800000
VITE_MAX_RETRIES=3
VITE_SAVE_INTERVAL=30000

# 性能配置
VITE_LAZY_LOAD_THRESHOLD=0.1
VITE_IMAGE_QUALITY=80
VITE_BUNDLE_ANALYZER=false

# 监控配置
VITE_LOG_LEVEL=info
VITE_ERROR_BOUNDARY_ENABLED=true
VITE_PERFORMANCE_BUDGET=5000
