{"name": "disc-assessment-website", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint .", "lint:fix": "eslint . --fix", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:watch": "vitest --watch", "type-check": "tsc --noEmit", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,js,jsx,json,css,md}\"", "analyze": "npm run build && npx vite-bundle-analyzer dist/stats.html", "deploy:staging": "bash scripts/deploy.sh staging", "deploy:production": "bash scripts/deploy.sh production", "clean": "rm -rf dist node_modules/.vite"}, "dependencies": {"framer-motion": "^11.0.8", "html2canvas": "^1.4.1", "jspdf": "^2.5.1", "lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-helmet-async": "^2.0.4", "react-router-dom": "^6.22.0"}, "devDependencies": {"@eslint/js": "^9.9.1", "@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^14.5.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "@vitest/coverage-v8": "^1.0.0", "@vitest/ui": "^1.0.0", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "husky": "^8.0.3", "jsdom": "^23.0.0", "lint-staged": "^15.0.0", "postcss": "^8.4.35", "prettier": "^3.0.0", "tailwindcss": "^3.4.1", "terser": "^5.43.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2", "vitest": "^1.0.0"}}