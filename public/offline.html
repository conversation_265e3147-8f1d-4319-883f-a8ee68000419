<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>离线模式 - DISC Traits</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
        }

        .container {
            background: white;
            border-radius: 20px;
            padding: 3rem;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 500px;
            width: 90%;
        }

        .icon {
            width: 80px;
            height: 80px;
            background: #f3f4f6;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 2rem;
            font-size: 2rem;
        }

        h1 {
            font-size: 2rem;
            margin-bottom: 1rem;
            color: #1f2937;
        }

        p {
            color: #6b7280;
            line-height: 1.6;
            margin-bottom: 2rem;
        }

        .features {
            background: #f9fafb;
            border-radius: 12px;
            padding: 1.5rem;
            margin: 2rem 0;
            text-align: left;
        }

        .features h3 {
            color: #374151;
            margin-bottom: 1rem;
            font-size: 1.1rem;
        }

        .features ul {
            list-style: none;
            color: #6b7280;
        }

        .features li {
            padding: 0.5rem 0;
            position: relative;
            padding-left: 1.5rem;
        }

        .features li::before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #10b981;
            font-weight: bold;
        }

        .buttons {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        .btn {
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.2s;
        }

        .btn-primary {
            background: #4f46e5;
            color: white;
        }

        .btn-primary:hover {
            background: #4338ca;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: #f3f4f6;
            color: #374151;
        }

        .btn-secondary:hover {
            background: #e5e7eb;
        }

        .status {
            margin-top: 2rem;
            padding: 1rem;
            background: #fef3c7;
            border-radius: 8px;
            color: #92400e;
            font-size: 0.9rem;
        }

        .status.online {
            background: #d1fae5;
            color: #065f46;
        }

        @media (max-width: 480px) {
            .container {
                padding: 2rem;
            }

            h1 {
                font-size: 1.5rem;
            }

            .buttons {
                flex-direction: column;
            }
        }

        .pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% {
                opacity: 1;
            }
            50% {
                opacity: 0.5;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon pulse">
            📡
        </div>
        
        <h1>您当前处于离线状态</h1>
        
        <p>
            看起来您的网络连接出现了问题。不过别担心，DISC Traits 的一些功能仍然可以在离线状态下使用。
        </p>

        <div class="features">
            <h3>离线可用功能：</h3>
            <ul>
                <li>查看已缓存的评估结果</li>
                <li>阅读DISC类型说明</li>
                <li>浏览常见问题</li>
                <li>查看使用指南</li>
            </ul>
        </div>

        <div class="buttons">
            <button class="btn btn-primary" onclick="checkConnection()">
                🔄 检查连接
            </button>
            <a href="/" class="btn btn-secondary">
                🏠 返回首页
            </a>
        </div>

        <div id="status" class="status">
            📶 正在检查网络连接状态...
        </div>
    </div>

    <script>
        // 检查网络连接状态
        function updateConnectionStatus() {
            const status = document.getElementById('status');
            
            if (navigator.onLine) {
                status.textContent = '✅ 网络连接已恢复！正在重新加载...';
                status.className = 'status online';
                
                // 延迟重新加载，让用户看到状态更新
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                status.textContent = '❌ 仍处于离线状态，请检查您的网络连接';
                status.className = 'status';
            }
        }

        // 手动检查连接
        function checkConnection() {
            const status = document.getElementById('status');
            status.textContent = '🔄 正在检查连接...';
            
            // 尝试获取一个小的资源来测试连接
            fetch('/favicon.ico', { 
                method: 'HEAD',
                cache: 'no-cache'
            })
            .then(() => {
                status.textContent = '✅ 网络连接已恢复！正在重新加载...';
                status.className = 'status online';
                setTimeout(() => {
                    window.location.href = '/';
                }, 1500);
            })
            .catch(() => {
                status.textContent = '❌ 仍无法连接到网络，请稍后再试';
                status.className = 'status';
            });
        }

        // 监听网络状态变化
        window.addEventListener('online', updateConnectionStatus);
        window.addEventListener('offline', updateConnectionStatus);

        // 页面加载时检查状态
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(updateConnectionStatus, 1000);
        });

        // 定期检查连接状态
        setInterval(() => {
            if (!navigator.onLine) {
                // 在离线状态下定期尝试连接
                fetch('/favicon.ico', { 
                    method: 'HEAD',
                    cache: 'no-cache'
                })
                .then(() => {
                    // 连接恢复，更新状态
                    updateConnectionStatus();
                })
                .catch(() => {
                    // 仍然离线，不做任何操作
                });
            }
        }, 10000); // 每10秒检查一次

        // 添加键盘快捷键
        document.addEventListener('keydown', (e) => {
            if (e.key === 'r' && (e.ctrlKey || e.metaKey)) {
                e.preventDefault();
                checkConnection();
            }
            
            if (e.key === 'h' && (e.ctrlKey || e.metaKey)) {
                e.preventDefault();
                window.location.href = '/';
            }
        });
    </script>
</body>
</html>
