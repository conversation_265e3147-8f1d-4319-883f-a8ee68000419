export interface Question {
  id: string;
  text: Record<string, string>;
  options: Array<{
    text: Record<string, string>;
    type: 'D' | 'I' | 'S' | 'C';
    value: number;
  }>;
}

export interface AssessmentResult {
  id: string;
  scores: {
    D: number;
    I: number;
    S: number;
    C: number;
  };
  percentages: {
    D: number;
    I: number;
    S: number;
    C: number;
  };
  primaryType: 'D' | 'I' | 'S' | 'C';
  completedAt: string;
  language: string;
  totalTime: number;
  questionTimes: number[];
  accuracy?: number;
  sessionId: string;
}

export interface UserResponse {
  questionId: string;
  selectedOption: number;
  type: 'D' | 'I' | 'S' | 'C';
  value: number;
  responseTime: number;
  timestamp: number;
}

export interface AssessmentSession {
  id: string;
  startTime: number;
  currentQuestion: number;
  responses: UserResponse[];
  isCompleted: boolean;
  deviceInfo: string;
  ipHash?: string;
  suspiciousActivity: string[];
}

export interface SecurityEvent {
  type: 'tab_switch' | 'window_blur' | 'copy_attempt' | 'paste_attempt' | 'right_click' | 'dev_tools' | 'multiple_sessions';
  timestamp: number;
  details?: any;
}

export type Language = 'en' | 'es' | 'zh';

export interface Translation {
  [key: string]: string | Translation;
}