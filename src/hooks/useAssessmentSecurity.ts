import { useState, useEffect, useCallback } from 'react';
import { SecurityEvent } from '../types';

export const useAssessmentSecurity = () => {
  const [securityEvents, setSecurityEvents] = useState<SecurityEvent[]>([]);
  const [isSecure, setIsSecure] = useState(true);
  const [warningCount, setWarningCount] = useState(0);

  const addSecurityEvent = useCallback((event: SecurityEvent) => {
    setSecurityEvents(prev => [...prev, event]);
    setWarningCount(prev => prev + 1);
    
    // 只有超过5次严重违规才标记为不安全
    if (warningCount >= 4) {
      setIsSecure(false);
    }
  }, [warningCount]);

  useEffect(() => {
    let isAssessmentActive = true;

    // 防止右键菜单 - 但不要太严格
    const handleContextMenu = (e: MouseEvent) => {
      if (!isAssessmentActive) return;
      e.preventDefault();
      // 只记录，不立即触发警告
      console.log('Right click detected');
    };

    // 防止复制粘贴 - 只针对关键操作
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isAssessmentActive) return;
      
      // 只阻止明显的作弊行为
      if (e.ctrlKey && e.key === 'c' && window.getSelection()?.toString().length > 50) {
        e.preventDefault();
        addSecurityEvent({
          type: 'copy_attempt',
          timestamp: Date.now()
        });
      }
      
      // 防止F12开发者工具
      if (e.key === 'F12' || (e.ctrlKey && e.shiftKey && e.key === 'I')) {
        e.preventDefault();
        addSecurityEvent({
          type: 'dev_tools',
          timestamp: Date.now()
        });
      }
    };

    // 监控窗口失焦 - 但要更宽松
    let blurCount = 0;
    const handleWindowBlur = () => {
      if (!isAssessmentActive) return;
      blurCount++;
      // 只有频繁切换才记录
      if (blurCount > 3) {
        addSecurityEvent({
          type: 'window_blur',
          timestamp: Date.now()
        });
      }
    };

    // 监控标签页切换 - 更宽松的策略
    let tabSwitchCount = 0;
    const handleVisibilityChange = () => {
      if (!isAssessmentActive) return;
      if (document.hidden) {
        tabSwitchCount++;
        // 只有多次切换才记录
        if (tabSwitchCount > 2) {
          addSecurityEvent({
            type: 'tab_switch',
            timestamp: Date.now()
          });
        }
      }
    };

    // 添加事件监听器
    document.addEventListener('contextmenu', handleContextMenu);
    document.addEventListener('keydown', handleKeyDown);
    window.addEventListener('blur', handleWindowBlur);
    document.addEventListener('visibilitychange', handleVisibilityChange);

    // 清理函数
    return () => {
      isAssessmentActive = false;
      document.removeEventListener('contextmenu', handleContextMenu);
      document.removeEventListener('keydown', handleKeyDown);
      window.removeEventListener('blur', handleWindowBlur);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, [addSecurityEvent]);

  // 移除多设备登录检查，因为它可能导致误判
  useEffect(() => {
    // 简化会话管理
    const sessionId = Date.now().toString();
    localStorage.setItem('disc-session-id', sessionId);
  }, []);

  return {
    securityEvents,
    isSecure,
    warningCount,
    addSecurityEvent
  };
};