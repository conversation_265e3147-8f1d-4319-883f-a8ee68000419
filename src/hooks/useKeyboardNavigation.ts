import { useEffect } from 'react';

interface UseKeyboardNavigationProps {
  onOptionSelect: (index: number) => void;
  onNext: () => void;
  onPrevious: () => void;
  selectedOption: number | null;
  optionsCount: number;
  canGoNext: boolean;
  canGoPrevious: boolean;
}

export const useKeyboardNavigation = ({
  onOptionSelect,
  onNext,
  onPrevious,
  selectedOption,
  optionsCount,
  canGoNext,
  canGoPrevious
}: UseKeyboardNavigationProps) => {
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // 数字键选择选项
      const num = parseInt(e.key);
      if (num >= 1 && num <= optionsCount) {
        e.preventDefault();
        onOptionSelect(num - 1);
        return;
      }

      // 回车确认
      if (e.key === 'Enter' && canGoNext) {
        e.preventDefault();
        onNext();
        return;
      }

      // 方向键导航
      if (e.key === 'ArrowUp' || e.key === 'ArrowDown') {
        e.preventDefault();
        if (selectedOption !== null) {
          const newIndex = e.key === 'ArrowUp' 
            ? Math.max(0, selectedOption - 1)
            : Math.min(optionsCount - 1, selectedOption + 1);
          onOptionSelect(newIndex);
        } else {
          onOptionSelect(0);
        }
        return;
      }

      // 左右箭头导航题目
      if (e.key === 'ArrowLeft' && canGoPrevious) {
        e.preventDefault();
        onPrevious();
        return;
      }

      if (e.key === 'ArrowRight' && canGoNext) {
        e.preventDefault();
        onNext();
        return;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [onOptionSelect, onNext, onPrevious, selectedOption, optionsCount, canGoNext, canGoPrevious]);
};