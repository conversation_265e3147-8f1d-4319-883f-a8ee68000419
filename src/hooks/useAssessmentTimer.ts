import { useState, useEffect, useRef } from 'react';

export const useAssessmentTimer = () => {
  const [totalTime, setTotalTime] = useState(0);
  const [questionStartTime, setQuestionStartTime] = useState(Date.now());
  const [questionTimes, setQuestionTimes] = useState<number[]>([]);
  const intervalRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    intervalRef.current = setInterval(() => {
      setTotalTime(prev => prev + 1);
    }, 1000);

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, []);

  const startQuestionTimer = () => {
    setQuestionStartTime(Date.now());
  };

  const endQuestionTimer = () => {
    const questionTime = Math.round((Date.now() - questionStartTime) / 1000);
    setQuestionTimes(prev => [...prev, questionTime]);
    return questionTime;
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  return {
    totalTime,
    questionTimes,
    startQuestionTimer,
    endQuestionTimer,
    formatTime
  };
};