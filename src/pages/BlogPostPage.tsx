import React from 'react';
import { motion } from 'framer-motion';
import { <PERSON>Left, Calendar, Clock, User, Share2, <PERSON>O<PERSON>, ArrowRight } from 'lucide-react';
import { useLanguage } from '../contexts/LanguageContext';
import { SEOHelmet } from '../components/SEOHelmet';
import { Link, useParams } from 'react-router-dom';
import { blogPosts } from '../data/blogPosts';

export const BlogPostPage: React.FC = () => {
  const { t, language } = useLanguage();
  const { slug } = useParams<{ slug: string }>();
  const currentPost = slug ? blogPosts.find(post => post.id === slug) : null;



  if (!currentPost) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-800 mb-4">{t('blog.postNotFound')}</h1>
          <Link to="/blog" className="text-indigo-600 hover:text-indigo-700">
            {t('blog.backToBlog')}
          </Link>
        </div>
      </div>
    );
  }

  const relatedPosts = blogPosts
    .filter(post => post.id !== slug)
    .slice(0, 2);

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50">
      <SEOHelmet
        title={currentPost.title[language]}
        description={currentPost.excerpt[language]}
        keywords={`${currentPost.category[language]}, DISC, 人格评估, ${currentPost.title[language]}`}
        type="article"
        author={currentPost.author[language]}
        publishedTime={currentPost.date}
        structuredDataType="article"
        structuredData={{
          title: currentPost.title[language],
          description: currentPost.excerpt[language],
          author: currentPost.author[language],
          publishedDate: currentPost.date,
          category: currentPost.category[language],
          url: window.location.href
        }}
      />
      
      {/* Header */}
      <div className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <Link
            to="/blog"
            className="inline-flex items-center space-x-2 text-indigo-600 hover:text-indigo-700 transition-colors"
          >
            <ArrowLeft className="w-5 h-5" />
            <span>{t('blog.backToBlog')}</span>
          </Link>
        </div>
      </div>

      <article className="max-w-4xl mx-auto px-4 py-16">
        {/* Article Header */}
        <motion.header
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="mb-12"
        >
          <div className="text-center mb-8">
            <div className="inline-block bg-indigo-600 text-white px-4 py-2 rounded-full text-sm font-medium mb-4">
              {currentPost.category[language]}
            </div>
            <h1 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6 leading-tight">
              {currentPost.title[language]}
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              {currentPost.excerpt[language]}
            </p>

            {/* Article Meta */}
            <div className="flex items-center justify-center space-x-6 text-gray-500">
              <div className="flex items-center space-x-2">
                <User className="w-5 h-5" />
                <span>{currentPost.author[language]}</span>
              </div>
              <div className="flex items-center space-x-2">
                <Calendar className="w-5 h-5" />
                <span>{currentPost.date}</span>
              </div>
              <div className="flex items-center space-x-2">
                <Clock className="w-5 h-5" />
                <span>{currentPost.readTime[language]}</span>
              </div>
            </div>
          </div>
        </motion.header>

        {/* Article Content */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="bg-white rounded-2xl shadow-xl p-8 md:p-12 mb-12"
        >
          <div
            className="prose prose-lg max-w-none prose-indigo prose-headings:text-gray-800 prose-p:text-gray-700 prose-li:text-gray-700"
            dangerouslySetInnerHTML={{ __html: currentPost.content[language] }}
          />
        </motion.div>

        {/* Share Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="bg-white rounded-2xl shadow-xl p-8 mb-12"
        >
          <div className="text-center">
            <h3 className="text-xl font-bold text-gray-800 mb-4">{t('blog.share.title')}</h3>
            <div className="flex justify-center space-x-4">
              <button className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                <Share2 className="w-4 h-4" />
                <span>{t('blog.share.wechat')}</span>
              </button>
              <button className="flex items-center space-x-2 px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
                <Share2 className="w-4 h-4" />
                <span>{t('blog.share.weibo')}</span>
              </button>
              <button className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                <Share2 className="w-4 h-4" />
                <span>{t('blog.share.linkedin')}</span>
              </button>
            </div>
          </div>
        </motion.div>

        {/* Related Posts */}
        {relatedPosts.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
            className="bg-white rounded-2xl shadow-xl p-8"
          >
            <h3 className="text-2xl font-bold text-gray-800 mb-6 flex items-center">
              <BookOpen className="w-6 h-6 text-indigo-600 mr-3" />
              {t('blog.relatedArticles')}
            </h3>
            <div className="grid md:grid-cols-2 gap-6">
              {relatedPosts.map((post) => (
                <Link
                  key={post.id}
                  to={`/blog/${post.id}`}
                  className="group border border-gray-200 rounded-lg p-6 hover:shadow-lg transition-shadow"
                >
                  <div className="mb-3">
                    <span className="inline-block bg-indigo-100 text-indigo-800 px-3 py-1 rounded-full text-sm font-medium">
                      {post.category[language]}
                    </span>
                  </div>
                  <h4 className="text-lg font-semibold text-gray-800 mb-2 group-hover:text-indigo-600 transition-colors">
                    {post.title[language]}
                  </h4>
                  <p className="text-gray-600 text-sm mb-4 line-clamp-2">
                    {post.excerpt[language]}
                  </p>
                  <div className="flex items-center justify-between text-sm text-gray-500">
                    <span>{post.author[language]}</span>
                    <div className="flex items-center space-x-1 text-indigo-600 group-hover:text-indigo-700">
                      <span>阅读更多</span>
                      <ArrowRight className="w-4 h-4" />
                    </div>
                  </div>
                </Link>
              ))}
            </div>
          </motion.div>
        )}
      </article>
    </div>
  );
};
