import React from 'react';
import { motion } from 'framer-motion';
import { ArrowLeft, Users, Target, Award, BookOpen } from 'lucide-react';
import { useLanguage } from '../contexts/LanguageContext';
import { SEOHelmet } from '../components/SEOHelmet';
import { Link } from 'react-router-dom';

export const AboutPage: React.FC = () => {
  const { t } = useLanguage();

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50">
      <SEOHelmet
        title={t('pages.about.title')}
        description={t('pages.about.metaDescription')}
        keywords="DISC评估, 人格测试, 行为风格, 团队建设, 领导力发展"
        structuredDataType="article"
        structuredData={{
          title: t('pages.about.title'),
          description: t('pages.about.metaDescription'),
          author: 'DISC Traits Team',
          category: 'Education'
        }}
      />
      
      {/* Header */}
      <div className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <Link 
            to="/" 
            className="inline-flex items-center space-x-2 text-indigo-600 hover:text-indigo-700 transition-colors"
          >
            <ArrowLeft className="w-5 h-5" />
            <span>{t('common.returnHome')}</span>
          </Link>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 py-16">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="bg-white rounded-2xl shadow-xl p-8 md:p-12"
        >
          {/* Page Title */}
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6">
              {t('pages.about.title')}
            </h1>
            <p className="text-xl text-gray-600 leading-relaxed">
              {t('pages.about.subtitle')}
            </p>
          </div>

          {/* Introduction */}
          <div className="prose max-w-none mb-12">
            <p className="text-lg text-gray-700 leading-relaxed mb-6">
              {t('pages.about.intro')}
            </p>
          </div>

          {/* What is DISC Section */}
          <div className="mb-12">
            <h2 className="text-3xl font-bold text-gray-800 mb-6 flex items-center">
              <BookOpen className="w-8 h-8 text-indigo-600 mr-3" />
              {t('pages.about.whatIsDisc')}
            </h2>
            <div className="bg-indigo-50 rounded-xl p-6 mb-6">
              <p className="text-gray-700 leading-relaxed">
                {t('pages.about.discDescription')}
              </p>
            </div>
            
            {/* DISC Types Grid */}
            <div className="grid md:grid-cols-2 gap-6 mt-8">
              {['D', 'I', 'S', 'C'].map((type) => (
                <div key={type} className="bg-white border border-gray-200 rounded-xl p-6 hover:shadow-lg transition-shadow">
                  <div className="flex items-center mb-4">
                    <div className={`w-12 h-12 rounded-full flex items-center justify-center text-white font-bold text-xl ${
                      type === 'D' ? 'bg-red-500' :
                      type === 'I' ? 'bg-yellow-500' :
                      type === 'S' ? 'bg-green-500' : 'bg-blue-500'
                    }`}>
                      {type}
                    </div>
                    <h3 className="text-xl font-semibold text-gray-800 ml-4">
                      {t(`types.${type}.name`)}
                    </h3>
                  </div>
                  <p className="text-gray-600">
                    {t(`types.${type}.shortDesc`)}
                  </p>
                </div>
              ))}
            </div>
          </div>

          {/* Our Mission */}
          <div className="mb-12">
            <h2 className="text-3xl font-bold text-gray-800 mb-6 flex items-center">
              <Target className="w-8 h-8 text-indigo-600 mr-3" />
              {t('pages.about.ourMission')}
            </h2>
            <p className="text-gray-700 leading-relaxed">
              {t('pages.about.missionDescription')}
            </p>
          </div>

          {/* Why Choose Us */}
          <div className="mb-12">
            <h2 className="text-3xl font-bold text-gray-800 mb-6 flex items-center">
              <Award className="w-8 h-8 text-indigo-600 mr-3" />
              {t('pages.about.whyChooseUs')}
            </h2>
            <div className="grid md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-indigo-600 rounded-full flex items-center justify-center mt-1">
                    <span className="text-white text-sm">✓</span>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-800">{t('pages.about.scientificBased')}</h4>
                    <p className="text-gray-600">{t('pages.about.scientificBasedDesc')}</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-indigo-600 rounded-full flex items-center justify-center mt-1">
                    <span className="text-white text-sm">✓</span>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-800">{t('pages.about.easyToUse')}</h4>
                    <p className="text-gray-600">{t('pages.about.easyToUseDesc')}</p>
                  </div>
                </div>
              </div>
              <div className="space-y-4">
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-indigo-600 rounded-full flex items-center justify-center mt-1">
                    <span className="text-white text-sm">✓</span>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-800">{t('pages.about.comprehensive')}</h4>
                    <p className="text-gray-600">{t('pages.about.comprehensiveDesc')}</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-indigo-600 rounded-full flex items-center justify-center mt-1">
                    <span className="text-white text-sm">✓</span>
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-800">{t('pages.about.multilingual')}</h4>
                    <p className="text-gray-600">{t('pages.about.multilingualDesc')}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Team Section */}
          <div className="mb-12">
            <h2 className="text-3xl font-bold text-gray-800 mb-6 flex items-center">
              <Users className="w-8 h-8 text-indigo-600 mr-3" />
              {t('pages.about.ourTeam')}
            </h2>
            <p className="text-gray-700 leading-relaxed">
              {t('pages.about.teamDescription')}
            </p>
          </div>

          {/* CTA Section */}
          <div className="text-center bg-gradient-to-r from-indigo-600 to-purple-600 rounded-xl p-8 text-white">
            <h3 className="text-2xl font-bold mb-4">{t('pages.about.readyToStart')}</h3>
            <p className="mb-6 opacity-90">{t('pages.about.startDescription')}</p>
            <Link 
              to="/assessment" 
              className="inline-flex items-center px-8 py-3 bg-white text-indigo-600 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
            >
              {t('common.start')}
            </Link>
          </div>
        </motion.div>
      </div>
    </div>
  );
};
