import React from 'react';
import { motion } from 'framer-motion';
import { ArrowLeft, FileText, Scale, AlertCircle, CheckCircle, XCircle, Info } from 'lucide-react';
import { useLanguage } from '../contexts/LanguageContext';
import { SEOHelmet } from '../components/SEOHelmet';
import { Link } from 'react-router-dom';

export const TermsPage: React.FC = () => {
  const { t } = useLanguage();

  const sections = [
    {
      icon: CheckCircle,
      title: '服务使用',
      content: [
        '本服务免费提供给个人用户进行自我评估。',
        '您必须诚实回答评估问题以获得准确结果。',
        '禁止使用自动化工具或脚本进行评估。',
        '每次评估应由同一人完成，不得代替他人作答。'
      ]
    },
    {
      icon: Scale,
      title: '知识产权',
      content: [
        'DISC评估内容和算法受版权保护。',
        '您可以分享个人评估结果，但不得复制评估内容。',
        '商业使用需要获得明确授权。',
        '我们保留修改或更新评估内容的权利。'
      ]
    },
    {
      icon: AlertCircle,
      title: '免责声明',
      content: [
        '评估结果仅供参考，不构成专业心理学建议。',
        '我们不对基于评估结果做出的决定承担责任。',
        '如需专业心理健康支持，请咨询合格的专业人士。',
        '评估不能替代正式的心理学评估或诊断。'
      ]
    },
    {
      icon: XCircle,
      title: '禁止行为',
      content: [
        '不得尝试破解、逆向工程或干扰服务。',
        '不得发布虚假或误导性的评估结果。',
        '不得将服务用于非法或有害目的。',
        '不得侵犯其他用户的权利或隐私。'
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50">
      <SEOHelmet 
        title="服务条款"
        description="DISC Traits服务条款和使用协议。了解使用我们的DISC人格评估服务的规则和条件。"
        keywords="服务条款, 使用协议, 法律条款, DISC评估规则"
      />
      
      {/* Header */}
      <div className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <Link 
            to="/" 
            className="inline-flex items-center space-x-2 text-indigo-600 hover:text-indigo-700 transition-colors"
          >
            <ArrowLeft className="w-5 h-5" />
            <span>{t('common.returnHome')}</span>
          </Link>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 py-16">
        {/* Page Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <div className="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <FileText className="w-8 h-8 text-indigo-600" />
          </div>
          <h1 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6">
            服务条款
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            使用DISC Traits服务即表示您同意遵守以下条款和条件。
          </p>
          <div className="mt-4 text-sm text-gray-500">
            最后更新：2025年1月15日
          </div>
        </motion.div>

        {/* Introduction */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="bg-white rounded-2xl shadow-xl p-8 mb-8"
        >
          <h2 className="text-2xl font-bold text-gray-800 mb-4">协议接受</h2>
          <div className="prose max-w-none text-gray-700">
            <p className="mb-4">
              欢迎使用DISC Traits！这些服务条款（"条款"）构成您与DISC Traits（"我们"、"我们的"或"服务"）之间的法律协议。
            </p>
            <p className="mb-4">
              通过访问或使用我们的服务，您确认您已阅读、理解并同意受这些条款的约束。如果您不同意这些条款，请不要使用我们的服务。
            </p>
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <Info className="w-5 h-5 text-blue-600 mt-0.5 flex-shrink-0" />
                <p className="text-blue-800 text-sm">
                  <strong>重要提醒：</strong>本服务仅供教育和自我了解目的。评估结果不应用于医疗、法律或就业决策。
                </p>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Terms Sections */}
        <div className="space-y-6">
          {sections.map((section, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.3 + index * 0.1 }}
              className="bg-white rounded-2xl shadow-xl p-8"
            >
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center mr-4">
                  <section.icon className="w-6 h-6 text-indigo-600" />
                </div>
                <h2 className="text-2xl font-bold text-gray-800">{section.title}</h2>
              </div>
              <ul className="space-y-3">
                {section.content.map((item, itemIndex) => (
                  <li key={itemIndex} className="flex items-start space-x-3">
                    <div className="w-2 h-2 bg-indigo-600 rounded-full mt-2 flex-shrink-0" />
                    <span className="text-gray-700">{item}</span>
                  </li>
                ))}
              </ul>
            </motion.div>
          ))}
        </div>

        {/* Service Availability */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.7 }}
          className="bg-white rounded-2xl shadow-xl p-8 mt-8"
        >
          <h2 className="text-2xl font-bold text-gray-800 mb-6">服务可用性</h2>
          <div className="space-y-4 text-gray-700">
            <p>
              我们努力确保服务的持续可用性，但不能保证服务不会中断。我们保留以下权利：
            </p>
            <div className="grid md:grid-cols-2 gap-6">
              <div className="border border-gray-200 rounded-lg p-4">
                <h3 className="font-semibold text-gray-800 mb-2">维护和更新</h3>
                <p className="text-sm text-gray-600">
                  我们可能需要暂时中断服务进行维护、更新或改进。
                </p>
              </div>
              <div className="border border-gray-200 rounded-lg p-4">
                <h3 className="font-semibold text-gray-800 mb-2">服务修改</h3>
                <p className="text-sm text-gray-600">
                  我们保留随时修改、暂停或终止服务的权利。
                </p>
              </div>
              <div className="border border-gray-200 rounded-lg p-4">
                <h3 className="font-semibold text-gray-800 mb-2">技术要求</h3>
                <p className="text-sm text-gray-600">
                  服务需要现代浏览器和稳定的互联网连接。
                </p>
              </div>
              <div className="border border-gray-200 rounded-lg p-4">
                <h3 className="font-semibold text-gray-800 mb-2">用户责任</h3>
                <p className="text-sm text-gray-600">
                  用户负责维护设备安全和数据备份。
                </p>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Limitation of Liability */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
          className="bg-white rounded-2xl shadow-xl p-8 mt-8"
        >
          <h2 className="text-2xl font-bold text-gray-800 mb-6">责任限制</h2>
          <div className="space-y-4 text-gray-700">
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div className="flex items-start space-x-3">
                <AlertCircle className="w-5 h-5 text-yellow-600 mt-0.5 flex-shrink-0" />
                <div>
                  <h3 className="font-semibold text-yellow-800 mb-2">重要声明</h3>
                  <p className="text-yellow-700 text-sm">
                    在法律允许的最大范围内，我们不对因使用或无法使用本服务而产生的任何直接、间接、偶然、特殊或后果性损害承担责任。
                  </p>
                </div>
              </div>
            </div>
            <div className="space-y-3">
              <h3 className="font-semibold text-gray-800">具体包括但不限于：</h3>
              <ul className="space-y-2">
                <li className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-gray-400 rounded-full mt-2 flex-shrink-0" />
                  <span className="text-sm">基于评估结果做出的个人或职业决定</span>
                </li>
                <li className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-gray-400 rounded-full mt-2 flex-shrink-0" />
                  <span className="text-sm">数据丢失或技术故障造成的损失</span>
                </li>
                <li className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-gray-400 rounded-full mt-2 flex-shrink-0" />
                  <span className="text-sm">第三方服务中断或错误</span>
                </li>
                <li className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-gray-400 rounded-full mt-2 flex-shrink-0" />
                  <span className="text-sm">误解或误用评估结果</span>
                </li>
              </ul>
            </div>
          </div>
        </motion.div>

        {/* Governing Law */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.9 }}
          className="bg-white rounded-2xl shadow-xl p-8 mt-8"
        >
          <h2 className="text-2xl font-bold text-gray-800 mb-6">适用法律和争议解决</h2>
          <div className="space-y-4 text-gray-700">
            <p>
              这些条款受中华人民共和国法律管辖。任何争议应首先通过友好协商解决。
            </p>
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold text-gray-800 mb-2">管辖法院</h3>
                <p className="text-sm">
                  如协商不成，争议应提交至北京市朝阳区人民法院管辖。
                </p>
              </div>
              <div>
                <h3 className="font-semibold text-gray-800 mb-2">条款修改</h3>
                <p className="text-sm">
                  我们保留随时修改这些条款的权利，修改后的条款将在网站上公布。
                </p>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Contact Information */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 1.0 }}
          className="bg-gradient-to-r from-indigo-600 to-purple-600 rounded-2xl p-8 text-white mt-8"
        >
          <h2 className="text-2xl font-bold mb-4">联系我们</h2>
          <p className="mb-6 opacity-90">
            如果您对这些服务条款有任何问题，请联系我们：
          </p>
          <div className="grid md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-semibold mb-2">法律事务</h3>
              <a href="mailto:<EMAIL>" className="text-white hover:text-gray-200">
                <EMAIL>
              </a>
            </div>
            <div>
              <h3 className="font-semibold mb-2">一般咨询</h3>
              <a href="mailto:<EMAIL>" className="text-white hover:text-gray-200">
                <EMAIL>
              </a>
            </div>
          </div>
          <div className="mt-6 pt-6 border-t border-white/20">
            <p className="text-sm opacity-80">
              继续使用我们的服务即表示您接受最新版本的服务条款。
            </p>
          </div>
        </motion.div>
      </div>
    </div>
  );
};
