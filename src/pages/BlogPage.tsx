import React from 'react';
import { motion } from 'framer-motion';
import { ArrowLeft, Calendar, Clock, User, ArrowRight } from 'lucide-react';
import { useLanguage } from '../contexts/LanguageContext';
import { SEOHelmet } from '../components/SEOHelmet';
import { Link } from 'react-router-dom';

export const BlogPage: React.FC = () => {
  const { t } = useLanguage();

  const blogPosts = [
    {
      id: 'disc-remote-team-management',
      title: t('blog.posts.remoteTeam.title'),
      excerpt: t('blog.posts.remoteTeam.excerpt'),
      author: t('blog.posts.remoteTeam.author'),
      date: '2025-01-10',
      readTime: `5${t('blog.readTime')}`,
      category: t('blog.categories.teamManagement'),
      image: 'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&h=450&q=80',
      featured: true
    },
    {
      id: 'disc-science-research-validation',
      title: t('blog.posts.science.title'),
      excerpt: t('blog.posts.science.excerpt'),
      author: t('blog.posts.science.author'),
      date: '2025-01-08',
      readTime: `7${t('blog.readTime')}`,
      category: t('blog.categories.research'),
      image: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&h=450&q=80',
      featured: true
    },
    {
      id: 'disc-sales-customer-types',
      title: t('blog.posts.sales.title'),
      excerpt: t('blog.posts.sales.excerpt'),
      author: t('blog.posts.sales.author'),
      date: '2025-01-05',
      readTime: `6${t('blog.readTime')}`,
      category: t('blog.categories.sales'),
      image: 'https://images.unsplash.com/photo-1556761175-b413da4baf72?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&h=450&q=80',
      featured: true
    },
    {
      id: 'disc-leadership-styles',
      title: t('blog.posts.leadership.title'),
      excerpt: t('blog.posts.leadership.excerpt'),
      author: t('blog.posts.leadership.author'),
      date: '2025-01-03',
      readTime: `8${t('blog.readTime')}`,
      category: t('blog.categories.leadership'),
      image: 'https://images.unsplash.com/photo-1519389950473-47ba0277781c?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&h=450&q=80',
      featured: false
    },
    {
      id: 'disc-communication-workplace',
      title: t('blog.posts.communication.title'),
      excerpt: t('blog.posts.communication.excerpt'),
      author: t('blog.posts.communication.author'),
      date: '2025-01-01',
      readTime: `5${t('blog.readTime')}`,
      category: t('blog.categories.communication'),
      image: 'https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&h=450&q=80',
      featured: false
    },
    {
      id: 'disc-hiring-recruitment',
      title: t('blog.posts.hiring.title'),
      excerpt: t('blog.posts.hiring.excerpt'),
      author: t('blog.posts.hiring.author'),
      date: '2024-12-28',
      readTime: `6${t('blog.readTime')}`,
      category: t('blog.categories.hr'),
      image: 'https://images.unsplash.com/photo-1600880292203-757bb62b4baf?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&h=450&q=80',
      featured: false
    }
  ];

  const categories = [
    t('blog.categories.all'),
    t('blog.categories.teamManagement'),
    t('blog.categories.research'),
    t('blog.categories.sales'),
    t('blog.categories.leadership'),
    t('blog.categories.communication'),
    t('blog.categories.hr')
  ];
  const [selectedCategory, setSelectedCategory] = React.useState(t('blog.categories.all'));

  const filteredPosts = selectedCategory === t('blog.categories.all')
    ? blogPosts
    : blogPosts.filter(post => post.category === selectedCategory);

  const featuredPosts = blogPosts.filter(post => post.featured);
  const regularPosts = blogPosts.filter(post => !post.featured);

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50">
      <SEOHelmet 
        title={t('pages.blog.title')}
        description={t('pages.blog.metaDescription')}
        keywords="DISC博客, 人格评估文章, 团队管理, 领导力发展, 职场沟通"
      />
      
      {/* Header */}
      <div className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <Link 
            to="/" 
            className="inline-flex items-center space-x-2 text-indigo-600 hover:text-indigo-700 transition-colors"
          >
            <ArrowLeft className="w-5 h-5" />
            <span>{t('common.returnHome')}</span>
          </Link>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 py-16">
        {/* Page Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="text-center mb-12"
        >
          <h1 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6">
            {t('pages.blog.title')}
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {t('pages.blog.subtitle')}
          </p>
        </motion.div>

        {/* Category Filter */}
        <div className="flex flex-wrap justify-center gap-3 mb-12">
          {categories.map((category) => (
            <button
              key={category}
              onClick={() => setSelectedCategory(category)}
              className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                selectedCategory === category
                  ? 'bg-indigo-600 text-white'
                  : 'bg-white text-gray-600 hover:bg-indigo-50 hover:text-indigo-600'
              }`}
            >
              {category}
            </button>
          ))}
        </div>

        {/* Featured Posts */}
        {selectedCategory === t('blog.categories.all') && (
          <div className="mb-16">
            <h2 className="text-2xl font-bold text-gray-800 mb-8">{t('blog.featuredArticles')}</h2>
            <div className="grid lg:grid-cols-3 gap-8">
              {featuredPosts.map((post, index) => (
                <motion.article
                  key={post.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  className="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow"
                >
                  <div className="aspect-video relative overflow-hidden">
                    <img
                      src={post.image}
                      alt={post.title}
                      className="w-full h-full object-cover"
                    />
                    <div className="absolute top-4 left-4">
                      <span className="bg-indigo-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                        {post.category}
                      </span>
                    </div>
                  </div>
                  <div className="p-6">
                    <h3 className="text-xl font-semibold text-gray-800 mb-3 line-clamp-2">
                      {post.title}
                    </h3>
                    <p className="text-gray-600 mb-4 line-clamp-3">
                      {post.excerpt}
                    </p>
                    <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                      <div className="flex items-center space-x-4">
                        <div className="flex items-center space-x-1">
                          <User className="w-4 h-4" />
                          <span>{post.author}</span>
                        </div>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Clock className="w-4 h-4" />
                        <span>{post.readTime}</span>
                      </div>
                    </div>
                    <Link
                      to={`/blog/${post.id}`}
                      className="inline-flex items-center text-indigo-600 hover:text-indigo-700 font-medium"
                    >
                      {t('blog.readMore')}
                      <ArrowRight className="w-4 h-4 ml-1" />
                    </Link>
                  </div>
                </motion.article>
              ))}
            </div>
          </div>
        )}

        {/* All Posts */}
        <div>
          <h2 className="text-2xl font-bold text-gray-800 mb-8">
            {selectedCategory === t('blog.categories.all') ? t('blog.allArticles') : `${selectedCategory}文章`}
          </h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredPosts.map((post, index) => (
              <motion.article
                key={post.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                className="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow"
              >
                <div className="aspect-video relative overflow-hidden">
                  <img
                    src={post.image}
                    alt={post.title}
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute top-4 left-4">
                    <span className="bg-indigo-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                      {post.category}
                    </span>
                  </div>
                </div>
                <div className="p-6">
                  <h3 className="text-lg font-semibold text-gray-800 mb-3 line-clamp-2">
                    {post.title}
                  </h3>
                  <p className="text-gray-600 mb-4 line-clamp-3">
                    {post.excerpt}
                  </p>
                  <div className="flex items-center justify-between text-sm text-gray-500 mb-4">
                    <div className="flex items-center space-x-1">
                      <User className="w-4 h-4" />
                      <span>{post.author}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Clock className="w-4 h-4" />
                      <span>{post.readTime}</span>
                    </div>
                  </div>
                  <Link
                    to={`/blog/${post.id}`}
                    className="inline-flex items-center text-indigo-600 hover:text-indigo-700 font-medium"
                  >
                    {t('blog.readMore')}
                    <ArrowRight className="w-4 h-4 ml-1" />
                  </Link>
                </div>
              </motion.article>
            ))}
          </div>
        </div>

        {/* Newsletter Subscription */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.3 }}
          className="mt-16 bg-gradient-to-r from-indigo-600 to-purple-600 rounded-2xl p-8 text-white text-center"
        >
          <h3 className="text-2xl font-bold mb-4">{t('blog.newsletter.title')}</h3>
          <p className="mb-6 opacity-90">{t('blog.newsletter.description')}</p>
          <div className="max-w-md mx-auto flex gap-3">
            <input
              type="email"
              placeholder={t('blog.newsletter.placeholder')}
              className="flex-1 px-4 py-3 rounded-lg text-gray-800"
            />
            <button className="px-6 py-3 bg-white text-indigo-600 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
              {t('blog.newsletter.subscribe')}
            </button>
          </div>
        </motion.div>
      </div>
    </div>
  );
};
