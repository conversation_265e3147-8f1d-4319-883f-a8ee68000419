import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Monitor,
  X,
  Activity,
  Zap,
  Database,
  Wifi,
  AlertTriangle,
  CheckCircle,
  Info
} from 'lucide-react';

interface PerformanceMetric {
  name: string;
  value: number;
  unit: string;
  status: 'good' | 'needs-improvement' | 'poor';
  threshold: { good: number; poor: number };
}

interface SystemInfo {
  userAgent: string;
  language: string;
  platform: string;
  cookieEnabled: boolean;
  onLine: boolean;
  memory?: {
    used: number;
    total: number;
    limit: number;
  };
}

interface NetworkRequest {
  name: string;
  duration: number;
  size: number;
  type: string;
}

export const DevDashboard: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [metrics, setMetrics] = useState<PerformanceMetric[]>([]);
  const [systemInfo, setSystemInfo] = useState<SystemInfo | null>(null);
  const [errors, setErrors] = useState<Error[]>([]);
  const [networkRequests, setNetworkRequests] = useState<NetworkRequest[]>([]);

  // 只在开发环境显示
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  useEffect(() => {
    if (isOpen) {
      collectMetrics();
      collectSystemInfo();
      collectErrors();
      collectNetworkRequests();
      
      const interval = setInterval(() => {
        collectMetrics();
        collectSystemInfo();
      }, 5000);
      
      return () => clearInterval(interval);
    }
  }, [isOpen]);

  const collectMetrics = () => {
    const newMetrics: PerformanceMetric[] = [];

    // 收集性能指标
    if ('performance' in window) {
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      
      if (navigation) {
        // 页面加载时间
        const loadTime = navigation.loadEventEnd - navigation.fetchStart;
        newMetrics.push({
          name: 'Page Load Time',
          value: Math.round(loadTime),
          unit: 'ms',
          status: loadTime <= 2000 ? 'good' : loadTime <= 4000 ? 'needs-improvement' : 'poor',
          threshold: { good: 2000, poor: 4000 }
        });

        // TTFB
        const ttfb = navigation.responseStart - navigation.fetchStart;
        newMetrics.push({
          name: 'TTFB',
          value: Math.round(ttfb),
          unit: 'ms',
          status: ttfb <= 800 ? 'good' : ttfb <= 1800 ? 'needs-improvement' : 'poor',
          threshold: { good: 800, poor: 1800 }
        });

        // DOM Content Loaded
        const dcl = navigation.domContentLoadedEventEnd - navigation.fetchStart;
        newMetrics.push({
          name: 'DOM Content Loaded',
          value: Math.round(dcl),
          unit: 'ms',
          status: dcl <= 1500 ? 'good' : dcl <= 3000 ? 'needs-improvement' : 'poor',
          threshold: { good: 1500, poor: 3000 }
        });
      }

      // 资源数量
      const resources = performance.getEntriesByType('resource');
      newMetrics.push({
        name: 'Resource Count',
        value: resources.length,
        unit: 'items',
        status: resources.length <= 50 ? 'good' : resources.length <= 100 ? 'needs-improvement' : 'poor',
        threshold: { good: 50, poor: 100 }
      });
    }

    setMetrics(newMetrics);
  };

  const collectSystemInfo = () => {
    const info: SystemInfo = {
      userAgent: navigator.userAgent,
      language: navigator.language,
      platform: navigator.platform,
      cookieEnabled: navigator.cookieEnabled,
      onLine: navigator.onLine
    };

    // 内存信息（如果可用）
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      info.memory = {
        used: Math.round(memory.usedJSHeapSize / 1024 / 1024),
        total: Math.round(memory.totalJSHeapSize / 1024 / 1024),
        limit: Math.round(memory.jsHeapSizeLimit / 1024 / 1024)
      };
    }

    setSystemInfo(info);
  };

  const collectErrors = () => {
    // 从localStorage获取错误日志
    const errorLog = localStorage.getItem('dev-error-log');
    if (errorLog) {
      try {
        setErrors(JSON.parse(errorLog));
      } catch (e) {
        setErrors([]);
      }
    }
  };

  const collectNetworkRequests = () => {
    if ('performance' in window) {
      const resources = performance.getEntriesByType('resource') as PerformanceResourceTiming[];
      const recentRequests = resources
        .slice(-10)
        .map(resource => ({
          name: resource.name.split('/').pop() || resource.name,
          duration: Math.round(resource.duration),
          size: resource.transferSize || 0,
          type: resource.initiatorType
        }));
      setNetworkRequests(recentRequests);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'good': return 'text-green-600 bg-green-50';
      case 'needs-improvement': return 'text-yellow-600 bg-yellow-50';
      case 'poor': return 'text-red-600 bg-red-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'good': return <CheckCircle className="w-4 h-4" />;
      case 'needs-improvement': return <AlertTriangle className="w-4 h-4" />;
      case 'poor': return <X className="w-4 h-4" />;
      default: return <Info className="w-4 h-4" />;
    }
  };

  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <>
      {/* 触发按钮 */}
      <button
        onClick={() => setIsOpen(true)}
        className="fixed bottom-4 left-4 z-40 bg-gray-800 text-white p-3 rounded-full shadow-lg hover:bg-gray-700 transition-colors"
        title="开发者仪表板"
      >
        <Monitor className="w-5 h-5" />
      </button>

      {/* 仪表板面板 */}
      <AnimatePresence>
        {isOpen && (
          <>
            {/* 背景遮罩 */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black bg-opacity-50 z-50"
              onClick={() => setIsOpen(false)}
            />

            {/* 面板 */}
            <motion.div
              initial={{ opacity: 0, x: -400 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -400 }}
              className="fixed top-0 left-0 h-full w-96 bg-white shadow-2xl z-50 overflow-y-auto"
            >
              <div className="p-6">
                {/* 标题 */}
                <div className="flex items-center justify-between mb-6">
                  <h2 className="text-xl font-bold text-gray-800 flex items-center">
                    <Monitor className="w-5 h-5 mr-2" />
                    开发者仪表板
                  </h2>
                  <button
                    onClick={() => setIsOpen(false)}
                    className="p-2 text-gray-500 hover:text-gray-700 rounded-lg hover:bg-gray-100"
                  >
                    <X className="w-5 h-5" />
                  </button>
                </div>

                {/* 性能指标 */}
                <div className="mb-6">
                  <h3 className="text-lg font-semibold text-gray-800 mb-3 flex items-center">
                    <Activity className="w-4 h-4 mr-2" />
                    性能指标
                  </h3>
                  <div className="space-y-3">
                    {metrics.map((metric, index) => (
                      <div key={index} className="bg-gray-50 rounded-lg p-3">
                        <div className="flex items-center justify-between mb-1">
                          <span className="text-sm font-medium text-gray-700">
                            {metric.name}
                          </span>
                          <div className={`flex items-center px-2 py-1 rounded-full text-xs ${getStatusColor(metric.status)}`}>
                            {getStatusIcon(metric.status)}
                            <span className="ml-1">{metric.status}</span>
                          </div>
                        </div>
                        <div className="text-lg font-bold text-gray-900">
                          {metric.value} {metric.unit}
                        </div>
                        <div className="text-xs text-gray-500">
                          Good: ≤{metric.threshold.good}{metric.unit} | Poor: &gt;{metric.threshold.poor}{metric.unit}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* 系统信息 */}
                {systemInfo && (
                  <div className="mb-6">
                    <h3 className="text-lg font-semibold text-gray-800 mb-3 flex items-center">
                      <Database className="w-4 h-4 mr-2" />
                      系统信息
                    </h3>
                    <div className="bg-gray-50 rounded-lg p-3 space-y-2">
                      <div className="flex items-center">
                        <Wifi className={`w-4 h-4 mr-2 ${systemInfo.onLine ? 'text-green-600' : 'text-red-600'}`} />
                        <span className="text-sm">
                          网络: {systemInfo.onLine ? '在线' : '离线'}
                        </span>
                      </div>
                      <div className="text-sm">
                        <span className="font-medium">语言:</span> {systemInfo.language}
                      </div>
                      <div className="text-sm">
                        <span className="font-medium">平台:</span> {systemInfo.platform}
                      </div>
                      <div className="text-sm">
                        <span className="font-medium">Cookie:</span> {systemInfo.cookieEnabled ? '启用' : '禁用'}
                      </div>
                      {systemInfo.memory && (
                        <div className="text-sm">
                          <span className="font-medium">内存:</span> {systemInfo.memory.used}MB / {systemInfo.memory.limit}MB
                          <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                            <div 
                              className="bg-blue-600 h-2 rounded-full" 
                              style={{ width: `${(systemInfo.memory.used / systemInfo.memory.limit) * 100}%` }}
                            ></div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* 网络请求 */}
                <div className="mb-6">
                  <h3 className="text-lg font-semibold text-gray-800 mb-3 flex items-center">
                    <Zap className="w-4 h-4 mr-2" />
                    最近请求
                  </h3>
                  <div className="space-y-2">
                    {networkRequests.map((request, index) => (
                      <div key={index} className="bg-gray-50 rounded-lg p-2">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium text-gray-700 truncate">
                            {request.name}
                          </span>
                          <span className="text-xs text-gray-500">
                            {request.type}
                          </span>
                        </div>
                        <div className="flex items-center justify-between text-xs text-gray-600">
                          <span>{request.duration}ms</span>
                          <span>{formatBytes(request.size)}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* 错误日志 */}
                {errors.length > 0 && (
                  <div className="mb-6">
                    <h3 className="text-lg font-semibold text-gray-800 mb-3 flex items-center">
                      <AlertTriangle className="w-4 h-4 mr-2 text-red-600" />
                      错误日志
                    </h3>
                    <div className="space-y-2">
                      {errors.slice(-5).map((error, index) => (
                        <div key={index} className="bg-red-50 border border-red-200 rounded-lg p-2">
                          <div className="text-sm font-medium text-red-800">
                            {error.message}
                          </div>
                          <div className="text-xs text-red-600">
                            {new Date(error.timestamp).toLocaleTimeString()}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* 操作按钮 */}
                <div className="space-y-2">
                  <button
                    onClick={() => window.location.reload()}
                    className="w-full px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    刷新页面
                  </button>
                  <button
                    onClick={() => {
                      localStorage.clear();
                      sessionStorage.clear();
                      window.location.reload();
                    }}
                    className="w-full px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                  >
                    清除缓存并刷新
                  </button>
                </div>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </>
  );
};

export default DevDashboard;
