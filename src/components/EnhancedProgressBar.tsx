import React from 'react';
import { motion } from 'framer-motion';
import { Clock, CheckCircle } from 'lucide-react';

interface EnhancedProgressBarProps {
  current: number;
  total: number;
  totalTime: number;
  className?: string;
  showTime?: boolean;
}

export const EnhancedProgressBar: React.FC<EnhancedProgressBarProps> = ({ 
  current, 
  total, 
  totalTime,
  className = '',
  showTime = true
}) => {
  const percentage = Math.round((current / total) * 100);
  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className={`w-full ${className}`}>
      <div className="flex justify-between items-center mb-3">
        <div className="flex items-center space-x-2">
          <CheckCircle className="w-5 h-5 text-indigo-600" />
          <span className="text-sm font-medium text-gray-700">
            Question {current} of {total}
          </span>
        </div>
        <div className="flex items-center space-x-4">
          <span className="text-sm font-bold text-indigo-600">{percentage}%</span>
          {showTime && (
            <div className="flex items-center space-x-1 text-sm text-gray-600">
              <Clock className="w-4 h-4" />
              <span>{formatTime(totalTime)}</span>
            </div>
          )}
        </div>
      </div>
      
      <div className="relative">
        <div className="w-full bg-gray-200 rounded-full h-3 overflow-hidden">
          <motion.div
            className="bg-gradient-to-r from-indigo-500 via-purple-500 to-indigo-600 h-3 rounded-full relative overflow-hidden"
            initial={{ width: 0 }}
            animate={{ width: `${percentage}%` }}
            transition={{ duration: 0.5, ease: 'easeOut' }}
          >
            <motion.div
              className="absolute inset-0 bg-white/20"
              animate={{ x: ['0%', '100%'] }}
              transition={{ duration: 2, repeat: Infinity, ease: 'linear' }}
              style={{ width: '30%' }}
            />
          </motion.div>
        </div>
        
        {/* Progress markers */}
        <div className="absolute top-0 left-0 w-full h-3 flex justify-between">
          {Array.from({ length: Math.min(total, 10) }, (_, i) => (
            <div
              key={i}
              className={`w-0.5 h-3 ${
                (i + 1) * (total / Math.min(total, 10)) <= current 
                  ? 'bg-white/50' 
                  : 'bg-gray-400/30'
              }`}
            />
          ))}
        </div>
      </div>
    </div>
  );
};