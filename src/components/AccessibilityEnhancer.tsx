import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  EyeOff,
  Type,
  Contrast,
  Settings,
  RotateCcw,
  Keyboard,
  MousePointer
} from 'lucide-react';

interface AccessibilitySettings {
  highContrast: boolean;
  largeText: boolean;
  reducedMotion: boolean;
  screenReader: boolean;
  keyboardNavigation: boolean;
  focusIndicators: boolean;
}

export const AccessibilityEnhancer: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [settings, setSettings] = useState<AccessibilitySettings>(() => {
    const saved = localStorage.getItem('accessibility-settings');
    return saved ? JSON.parse(saved) : {
      highContrast: false,
      largeText: false,
      reducedMotion: false,
      screenReader: false,
      keyboardNavigation: true,
      focusIndicators: true
    };
  });

  // 应用可访问性设置
  useEffect(() => {
    const root = document.documentElement;
    
    // 高对比度模式
    if (settings.highContrast) {
      root.classList.add('high-contrast');
    } else {
      root.classList.remove('high-contrast');
    }

    // 大字体模式
    if (settings.largeText) {
      root.classList.add('large-text');
    } else {
      root.classList.remove('large-text');
    }

    // 减少动画
    if (settings.reducedMotion) {
      root.classList.add('reduced-motion');
    } else {
      root.classList.remove('reduced-motion');
    }

    // 键盘导航
    if (settings.keyboardNavigation) {
      root.classList.add('keyboard-navigation');
    } else {
      root.classList.remove('keyboard-navigation');
    }

    // 焦点指示器
    if (settings.focusIndicators) {
      root.classList.add('focus-indicators');
    } else {
      root.classList.remove('focus-indicators');
    }

    // 保存设置
    localStorage.setItem('accessibility-settings', JSON.stringify(settings));
  }, [settings]);

  // 键盘快捷键
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Alt + A 打开可访问性面板
      if (e.altKey && e.key === 'a') {
        e.preventDefault();
        setIsOpen(!isOpen);
      }
      
      // Alt + C 切换高对比度
      if (e.altKey && e.key === 'c') {
        e.preventDefault();
        toggleSetting('highContrast');
      }
      
      // Alt + T 切换大字体
      if (e.altKey && e.key === 't') {
        e.preventDefault();
        toggleSetting('largeText');
      }
      
      // Alt + M 切换减少动画
      if (e.altKey && e.key === 'm') {
        e.preventDefault();
        toggleSetting('reducedMotion');
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen]);

  const toggleSetting = (key: keyof AccessibilitySettings) => {
    setSettings(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  const resetSettings = () => {
    setSettings({
      highContrast: false,
      largeText: false,
      reducedMotion: false,
      screenReader: false,
      keyboardNavigation: true,
      focusIndicators: true
    });
  };

  const accessibilityOptions = [
    {
      key: 'highContrast' as keyof AccessibilitySettings,
      label: '高对比度模式',
      description: '增强文本和背景的对比度',
      icon: Contrast,
      shortcut: 'Alt + C'
    },
    {
      key: 'largeText' as keyof AccessibilitySettings,
      label: '大字体模式',
      description: '增大文字大小以提高可读性',
      icon: Type,
      shortcut: 'Alt + T'
    },
    {
      key: 'reducedMotion' as keyof AccessibilitySettings,
      label: '减少动画',
      description: '减少页面动画和过渡效果',
      icon: EyeOff,
      shortcut: 'Alt + M'
    },
    {
      key: 'keyboardNavigation' as keyof AccessibilitySettings,
      label: '键盘导航',
      description: '启用键盘导航支持',
      icon: Keyboard,
      shortcut: 'Tab'
    },
    {
      key: 'focusIndicators' as keyof AccessibilitySettings,
      label: '焦点指示器',
      description: '显示键盘焦点的视觉指示器',
      icon: MousePointer,
      shortcut: 'Tab'
    }
  ];

  return (
    <>
      {/* 可访问性按钮 */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="fixed bottom-4 right-4 z-50 w-12 h-12 bg-indigo-600 text-white rounded-full shadow-lg hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition-colors"
        aria-label="打开可访问性设置"
        title="可访问性设置 (Alt + A)"
      >
        <Settings className="w-6 h-6 mx-auto" />
      </button>

      {/* 可访问性面板 */}
      <AnimatePresence>
        {isOpen && (
          <>
            {/* 背景遮罩 */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black bg-opacity-50 z-40"
              onClick={() => setIsOpen(false)}
            />

            {/* 面板 */}
            <motion.div
              initial={{ opacity: 0, x: 300 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 300 }}
              className="fixed top-0 right-0 h-full w-80 bg-white shadow-2xl z-50 overflow-y-auto"
              role="dialog"
              aria-labelledby="accessibility-title"
              aria-describedby="accessibility-description"
            >
              <div className="p-6">
                {/* 标题 */}
                <div className="flex items-center justify-between mb-6">
                  <h2 id="accessibility-title" className="text-xl font-bold text-gray-800">
                    可访问性设置
                  </h2>
                  <button
                    onClick={() => setIsOpen(false)}
                    className="p-2 text-gray-500 hover:text-gray-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 rounded"
                    aria-label="关闭可访问性设置"
                  >
                    ×
                  </button>
                </div>

                <p id="accessibility-description" className="text-sm text-gray-600 mb-6">
                  调整这些设置以改善您的浏览体验。设置会自动保存。
                </p>

                {/* 设置选项 */}
                <div className="space-y-4">
                  {accessibilityOptions.map((option) => (
                    <div key={option.key} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center mb-2">
                            <option.icon className="w-5 h-5 text-indigo-600 mr-2" />
                            <label 
                              htmlFor={option.key}
                              className="text-sm font-medium text-gray-800"
                            >
                              {option.label}
                            </label>
                          </div>
                          <p className="text-xs text-gray-600 mb-2">
                            {option.description}
                          </p>
                          <p className="text-xs text-gray-500">
                            快捷键: {option.shortcut}
                          </p>
                        </div>
                        <button
                          id={option.key}
                          onClick={() => toggleSetting(option.key)}
                          className={`ml-4 relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 ${
                            settings[option.key] ? 'bg-indigo-600' : 'bg-gray-200'
                          }`}
                          role="switch"
                          aria-checked={settings[option.key]}
                          aria-labelledby={option.key}
                        >
                          <span
                            className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                              settings[option.key] ? 'translate-x-6' : 'translate-x-1'
                            }`}
                          />
                        </button>
                      </div>
                    </div>
                  ))}
                </div>

                {/* 重置按钮 */}
                <div className="mt-6 pt-6 border-t border-gray-200">
                  <button
                    onClick={resetSettings}
                    className="w-full flex items-center justify-center px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                  >
                    <RotateCcw className="w-4 h-4 mr-2" />
                    重置所有设置
                  </button>
                </div>

                {/* 键盘快捷键说明 */}
                <div className="mt-6 p-4 bg-gray-50 rounded-lg">
                  <h3 className="text-sm font-medium text-gray-800 mb-2">键盘快捷键</h3>
                  <ul className="text-xs text-gray-600 space-y-1">
                    <li>Alt + A: 打开/关闭此面板</li>
                    <li>Alt + C: 切换高对比度模式</li>
                    <li>Alt + T: 切换大字体模式</li>
                    <li>Alt + M: 切换减少动画</li>
                    <li>Tab: 键盘导航</li>
                    <li>Enter/Space: 激活按钮</li>
                  </ul>
                </div>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </>
  );
};

export default AccessibilityEnhancer;
