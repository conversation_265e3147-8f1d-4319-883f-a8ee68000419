import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Download, X, RefreshCw } from 'lucide-react';

interface ServiceWorkerManagerProps {
  onUpdate?: () => void;
  onOffline?: () => void;
  onOnline?: () => void;
}

export const ServiceWorkerManager: React.FC<ServiceWorkerManagerProps> = ({
  onUpdate,
  onOffline,
  onOnline
}) => {
  const [showUpdatePrompt, setShowUpdatePrompt] = useState(false);
  const [isOffline, setIsOffline] = useState(!navigator.onLine);
  const [registration, setRegistration] = useState<ServiceWorkerRegistration | null>(null);

  useEffect(() => {
    // 注册Service Worker
    if ('serviceWorker' in navigator && process.env.NODE_ENV === 'production') {
      registerServiceWorker();
    }

    // 监听网络状态变化
    const handleOnline = () => {
      setIsOffline(false);
      onOnline?.();
    };

    const handleOffline = () => {
      setIsOffline(true);
      onOffline?.();
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [onOnline, onOffline]);

  const registerServiceWorker = async () => {
    try {
      const registration = await navigator.serviceWorker.register('/sw.js');
      setRegistration(registration);

      console.log('Service Worker registered successfully:', registration);

      // 检查更新
      registration.addEventListener('updatefound', () => {
        const newWorker = registration.installing;
        if (newWorker) {
          newWorker.addEventListener('statechange', () => {
            if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
              // 有新版本可用
              setShowUpdatePrompt(true);
              onUpdate?.();
            }
          });
        }
      });

      // 监听Service Worker消息
      navigator.serviceWorker.addEventListener('message', (event) => {
        if (event.data && event.data.type === 'SW_UPDATE_AVAILABLE') {
          setShowUpdatePrompt(true);
        }
      });

      // 检查是否有等待中的Service Worker
      if (registration.waiting) {
        setShowUpdatePrompt(true);
      }

    } catch (error) {
      console.error('Service Worker registration failed:', error);
    }
  };

  const handleUpdate = () => {
    if (registration?.waiting) {
      // 告诉等待中的Service Worker跳过等待
      registration.waiting.postMessage({ type: 'SKIP_WAITING' });
      
      // 监听控制器变化
      navigator.serviceWorker.addEventListener('controllerchange', () => {
        window.location.reload();
      });
    }
    setShowUpdatePrompt(false);
  };

  const dismissUpdate = () => {
    setShowUpdatePrompt(false);
  };

  return (
    <>
      {/* 更新提示 */}
      <AnimatePresence>
        {showUpdatePrompt && (
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 50 }}
            className="fixed bottom-4 left-4 right-4 md:left-auto md:right-4 md:w-96 bg-white rounded-lg shadow-2xl border border-gray-200 z-50"
          >
            <div className="p-4">
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0">
                  <div className="w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center">
                    <Download className="w-4 h-4 text-indigo-600" />
                  </div>
                </div>
                <div className="flex-1 min-w-0">
                  <h3 className="text-sm font-medium text-gray-900">
                    新版本可用
                  </h3>
                  <p className="text-sm text-gray-600 mt-1">
                    DISC Traits 有新版本可用，包含性能改进和新功能。
                  </p>
                </div>
                <button
                  onClick={dismissUpdate}
                  className="flex-shrink-0 p-1 text-gray-400 hover:text-gray-600 rounded"
                >
                  <X className="w-4 h-4" />
                </button>
              </div>
              
              <div className="mt-4 flex space-x-2">
                <button
                  onClick={handleUpdate}
                  className="flex items-center px-3 py-2 bg-indigo-600 text-white text-sm font-medium rounded-md hover:bg-indigo-700 transition-colors"
                >
                  <RefreshCw className="w-4 h-4 mr-1" />
                  立即更新
                </button>
                <button
                  onClick={dismissUpdate}
                  className="px-3 py-2 bg-gray-100 text-gray-700 text-sm font-medium rounded-md hover:bg-gray-200 transition-colors"
                >
                  稍后提醒
                </button>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* 离线状态指示器 */}
      <AnimatePresence>
        {isOffline && (
          <motion.div
            initial={{ opacity: 0, y: -50 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -50 }}
            className="fixed top-0 left-0 right-0 bg-yellow-500 text-white text-center py-2 px-4 z-50"
          >
            <div className="flex items-center justify-center space-x-2">
              <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
              <span className="text-sm font-medium">
                您当前处于离线状态，部分功能可能受限
              </span>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

// Service Worker 工具函数
export class ServiceWorkerUtils {
  // 检查Service Worker支持
  static isSupported(): boolean {
    return 'serviceWorker' in navigator;
  }

  // 获取注册信息
  static async getRegistration(): Promise<ServiceWorkerRegistration | null> {
    if (!this.isSupported()) return null;
    
    try {
      return await navigator.serviceWorker.getRegistration();
    } catch (error) {
      console.error('Failed to get service worker registration:', error);
      return null;
    }
  }

  // 检查更新
  static async checkForUpdates(): Promise<boolean> {
    const registration = await this.getRegistration();
    if (!registration) return false;

    try {
      await registration.update();
      return !!registration.waiting;
    } catch (error) {
      console.error('Failed to check for updates:', error);
      return false;
    }
  }

  // 清除缓存
  static async clearCache(): Promise<void> {
    if (!this.isSupported()) return;

    try {
      const cacheNames = await caches.keys();
      await Promise.all(
        cacheNames.map(cacheName => caches.delete(cacheName))
      );
      console.log('All caches cleared');
    } catch (error) {
      console.error('Failed to clear cache:', error);
    }
  }

  // 发送消息给Service Worker
  static async sendMessage(message: any): Promise<any> {
    if (!this.isSupported() || !navigator.serviceWorker.controller) {
      return null;
    }

    return new Promise((resolve) => {
      const messageChannel = new MessageChannel();
      messageChannel.port1.onmessage = (event) => {
        resolve(event.data);
      };

      navigator.serviceWorker.controller.postMessage(message, [messageChannel.port2]);
    });
  }

  // 获取缓存大小
  static async getCacheSize(): Promise<number> {
    if (!('storage' in navigator) || !('estimate' in navigator.storage)) {
      return 0;
    }

    try {
      const estimate = await navigator.storage.estimate();
      return estimate.usage || 0;
    } catch (error) {
      console.error('Failed to get cache size:', error);
      return 0;
    }
  }

  // 预缓存重要资源
  static async precacheResources(urls: string[]): Promise<void> {
    if (!('caches' in window)) return;

    try {
      const cache = await caches.open('disc-traits-precache');
      await cache.addAll(urls);
      console.log('Resources precached successfully');
    } catch (error) {
      console.error('Failed to precache resources:', error);
    }
  }
}

// 离线存储管理
export class OfflineStorage {
  private static readonly STORAGE_KEY = 'disc-traits-offline';

  // 保存离线数据
  static save(key: string, data: any): void {
    try {
      const offlineData = this.getAll();
      offlineData[key] = {
        data,
        timestamp: Date.now()
      };
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(offlineData));
    } catch (error) {
      console.error('Failed to save offline data:', error);
    }
  }

  // 获取离线数据
  static get(key: string): any {
    try {
      const offlineData = this.getAll();
      const item = offlineData[key];
      return item ? item.data : null;
    } catch (error) {
      console.error('Failed to get offline data:', error);
      return null;
    }
  }

  // 获取所有离线数据
  static getAll(): Record<string, any> {
    try {
      const data = localStorage.getItem(this.STORAGE_KEY);
      return data ? JSON.parse(data) : {};
    } catch (error) {
      console.error('Failed to get all offline data:', error);
      return {};
    }
  }

  // 清除过期数据
  static cleanup(maxAge: number = 7 * 24 * 60 * 60 * 1000): void {
    try {
      const offlineData = this.getAll();
      const now = Date.now();
      
      Object.keys(offlineData).forEach(key => {
        const item = offlineData[key];
        if (now - item.timestamp > maxAge) {
          delete offlineData[key];
        }
      });
      
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(offlineData));
    } catch (error) {
      console.error('Failed to cleanup offline data:', error);
    }
  }

  // 清除所有离线数据
  static clear(): void {
    try {
      localStorage.removeItem(this.STORAGE_KEY);
    } catch (error) {
      console.error('Failed to clear offline data:', error);
    }
  }
}

export default ServiceWorkerManager;
