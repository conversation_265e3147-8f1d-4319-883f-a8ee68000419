import React, { useEffect } from 'react';
import { Helmet } from 'react-helmet-async';

// 安全增强组件
export const SecurityEnhancer: React.FC = () => {
  useEffect(() => {
    // 防止右键菜单（可选，根据需求决定是否启用）
    const handleContextMenu = (e: MouseEvent) => {
      // 在生产环境中可以启用
      // e.preventDefault();
    };

    // 防止F12开发者工具（可选，根据需求决定是否启用）
    const handleKeyDown = (e: KeyboardEvent) => {
      // 在生产环境中可以启用
      // if (e.key === 'F12' || 
      //     (e.ctrlKey && e.shiftKey && e.key === 'I') ||
      //     (e.ctrlKey && e.shiftKey && e.key === 'C') ||
      //     (e.ctrlKey && e.key === 'U')) {
      //   e.preventDefault();
      // }
    };

    // 防止拖拽外部文件
    const handleDragOver = (e: DragEvent) => {
      e.preventDefault();
    };

    const handleDrop = (e: DragEvent) => {
      e.preventDefault();
    };

    // 检测开发者工具
    const detectDevTools = () => {
      const threshold = 160;
      setInterval(() => {
        if (window.outerHeight - window.innerHeight > threshold || 
            window.outerWidth - window.innerWidth > threshold) {
          // 开发者工具可能被打开
          console.clear();
        }
      }, 500);
    };

    // 防止控制台注入
    const protectConsole = () => {
      // 重写console方法以防止恶意代码注入
      const originalLog = console.log;
      const originalError = console.error;
      const originalWarn = console.warn;

      console.log = (...args) => {
        // 在生产环境中可以过滤敏感信息
        if (process.env.NODE_ENV === 'development') {
          originalLog.apply(console, args);
        }
      };

      console.error = (...args) => {
        if (process.env.NODE_ENV === 'development') {
          originalError.apply(console, args);
        }
      };

      console.warn = (...args) => {
        if (process.env.NODE_ENV === 'development') {
          originalWarn.apply(console, args);
        }
      };
    };

    // 防止iframe嵌入
    const preventFraming = () => {
      if (window.top !== window.self) {
        window.top!.location = window.self.location;
      }
    };

    // 清理敏感数据
    const clearSensitiveData = () => {
      // 页面卸载时清理敏感数据
      window.addEventListener('beforeunload', () => {
        // 清理localStorage中的敏感数据
        const sensitiveKeys = ['assessment-session', 'user-data'];
        sensitiveKeys.forEach(key => {
          if (localStorage.getItem(key)) {
            localStorage.removeItem(key);
          }
        });
      });
    };

    // 监控异常活动
    const monitorSuspiciousActivity = () => {
      let clickCount = 0;
      let lastClickTime = 0;

      document.addEventListener('click', () => {
        const now = Date.now();
        if (now - lastClickTime < 100) {
          clickCount++;
          if (clickCount > 10) {
            // 检测到可能的自动化点击
            console.warn('Suspicious activity detected');
            clickCount = 0;
          }
        } else {
          clickCount = 0;
        }
        lastClickTime = now;
      });
    };

    // 内容安全策略违规报告
    const handleCSPViolation = (e: SecurityPolicyViolationEvent) => {
      console.warn('CSP Violation:', {
        blockedURI: e.blockedURI,
        violatedDirective: e.violatedDirective,
        originalPolicy: e.originalPolicy
      });
      
      // 可以发送到监控服务
      // sendToMonitoring('csp_violation', e);
    };

    // 初始化安全措施
    if (process.env.NODE_ENV === 'production') {
      detectDevTools();
      protectConsole();
    }
    
    preventFraming();
    clearSensitiveData();
    monitorSuspiciousActivity();

    // 事件监听器
    document.addEventListener('contextmenu', handleContextMenu);
    document.addEventListener('keydown', handleKeyDown);
    document.addEventListener('dragover', handleDragOver);
    document.addEventListener('drop', handleDrop);
    document.addEventListener('securitypolicyviolation', handleCSPViolation);

    // 清理函数
    return () => {
      document.removeEventListener('contextmenu', handleContextMenu);
      document.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('dragover', handleDragOver);
      document.removeEventListener('drop', handleDrop);
      document.removeEventListener('securitypolicyviolation', handleCSPViolation);
    };
  }, []);

  return (
    <Helmet>
      {/* 内容安全策略 */}
      <meta 
        httpEquiv="Content-Security-Policy" 
        content="default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.googletagmanager.com https://www.google-analytics.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://www.google-analytics.com; frame-ancestors 'none';" 
      />
      
      {/* 严格传输安全 */}
      <meta 
        httpEquiv="Strict-Transport-Security" 
        content="max-age=31536000; includeSubDomains; preload" 
      />
      
      {/* 防止MIME类型嗅探 */}
      <meta httpEquiv="X-Content-Type-Options" content="nosniff" />
      
      {/* 防止点击劫持 */}
      <meta httpEquiv="X-Frame-Options" content="DENY" />
      
      {/* XSS保护 */}
      <meta httpEquiv="X-XSS-Protection" content="1; mode=block" />
      
      {/* 引用策略 */}
      <meta httpEquiv="Referrer-Policy" content="strict-origin-when-cross-origin" />
      
      {/* 权限策略 */}
      <meta 
        httpEquiv="Permissions-Policy" 
        content="camera=(), microphone=(), geolocation=(), payment=(), usb=(), magnetometer=(), gyroscope=(), accelerometer=()" 
      />
      
      {/* 防止缓存敏感页面 */}
      <meta httpEquiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
      <meta httpEquiv="Pragma" content="no-cache" />
      <meta httpEquiv="Expires" content="0" />
    </Helmet>
  );
};

// 数据加密工具
export class DataEncryption {
  private static key = 'disc-traits-2025';

  // 简单的字符串加密（实际项目中应使用更强的加密算法）
  static encrypt(text: string): string {
    try {
      return btoa(encodeURIComponent(text + this.key));
    } catch (error) {
      console.error('Encryption failed:', error);
      return text;
    }
  }

  // 简单的字符串解密
  static decrypt(encryptedText: string): string {
    try {
      const decoded = atob(encryptedText);
      const text = decodeURIComponent(decoded);
      return text.replace(this.key, '');
    } catch (error) {
      console.error('Decryption failed:', error);
      return encryptedText;
    }
  }

  // 安全存储到localStorage
  static secureStore(key: string, value: any): void {
    try {
      const encrypted = this.encrypt(JSON.stringify(value));
      localStorage.setItem(key, encrypted);
    } catch (error) {
      console.error('Secure store failed:', error);
    }
  }

  // 从localStorage安全读取
  static secureRetrieve(key: string): any {
    try {
      const encrypted = localStorage.getItem(key);
      if (!encrypted) return null;
      
      const decrypted = this.decrypt(encrypted);
      return JSON.parse(decrypted);
    } catch (error) {
      console.error('Secure retrieve failed:', error);
      return null;
    }
  }
}

// 输入验证工具
export class InputValidator {
  // 验证邮箱
  static isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email) && email.length <= 254;
  }

  // 验证URL
  static isValidURL(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  // 清理HTML标签
  static sanitizeHTML(input: string): string {
    const div = document.createElement('div');
    div.textContent = input;
    return div.innerHTML;
  }

  // 验证字符串长度
  static isValidLength(text: string, min: number, max: number): boolean {
    return text.length >= min && text.length <= max;
  }

  // 检测恶意脚本
  static containsScript(input: string): boolean {
    const scriptRegex = /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi;
    return scriptRegex.test(input);
  }
}

// 速率限制工具
export class RateLimiter {
  private static attempts: Map<string, number[]> = new Map();

  // 检查是否超过速率限制
  static isRateLimited(key: string, maxAttempts: number, windowMs: number): boolean {
    const now = Date.now();
    const attempts = this.attempts.get(key) || [];
    
    // 清理过期的尝试记录
    const validAttempts = attempts.filter(time => now - time < windowMs);
    
    if (validAttempts.length >= maxAttempts) {
      return true;
    }
    
    validAttempts.push(now);
    this.attempts.set(key, validAttempts);
    return false;
  }

  // 清理过期记录
  static cleanup(): void {
    const now = Date.now();
    for (const [key, attempts] of this.attempts.entries()) {
      const validAttempts = attempts.filter(time => now - time < 3600000); // 1小时
      if (validAttempts.length === 0) {
        this.attempts.delete(key);
      } else {
        this.attempts.set(key, validAttempts);
      }
    }
  }
}

export default SecurityEnhancer;
