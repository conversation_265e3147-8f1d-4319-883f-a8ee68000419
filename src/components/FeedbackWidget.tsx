import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  MessageCircle,
  X,
  Send,
  Star,
  Bug,
  Lightbulb,
  Heart
} from 'lucide-react';
import { useLanguage } from '../contexts/LanguageContext';

interface FeedbackData {
  type: 'bug' | 'suggestion' | 'general' | 'rating';
  rating?: number;
  message: string;
  email?: string;
  page: string;
  userAgent: string;
  timestamp: string;
}

export const FeedbackWidget: React.FC = () => {
  const { t } = useLanguage();
  const [isOpen, setIsOpen] = useState(false);
  const [step, setStep] = useState<'type' | 'form' | 'success'>('type');
  const [feedbackType, setFeedbackType] = useState<'bug' | 'suggestion' | 'general' | 'rating'>('general');
  const [rating, setRating] = useState(0);
  const [message, setMessage] = useState('');
  const [email, setEmail] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const feedbackTypes = [
    {
      type: 'bug' as const,
      icon: Bug,
      title: '报告问题',
      description: '发现了错误或问题？告诉我们！',
      color: 'text-red-600 bg-red-50 border-red-200'
    },
    {
      type: 'suggestion' as const,
      icon: Lightbulb,
      title: '功能建议',
      description: '有改进想法？我们很乐意听取！',
      color: 'text-yellow-600 bg-yellow-50 border-yellow-200'
    },
    {
      type: 'rating' as const,
      icon: Star,
      title: '评价体验',
      description: '为我们的服务打分并留下评价',
      color: 'text-blue-600 bg-blue-50 border-blue-200'
    },
    {
      type: 'general' as const,
      icon: MessageCircle,
      title: '一般反馈',
      description: '其他想法或建议',
      color: 'text-green-600 bg-green-50 border-green-200'
    }
  ];

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    const feedbackData: FeedbackData = {
      type: feedbackType,
      rating: feedbackType === 'rating' ? rating : undefined,
      message,
      email: email || undefined,
      page: window.location.pathname,
      userAgent: navigator.userAgent,
      timestamp: new Date().toISOString()
    };

    try {
      // 这里可以发送到后端API
      console.log('Feedback submitted:', feedbackData);
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setStep('success');
    } catch (error) {
      console.error('Failed to submit feedback:', error);
      alert('提交失败，请稍后重试');
    } finally {
      setIsSubmitting(false);
    }
  };

  const resetForm = () => {
    setStep('type');
    setFeedbackType('general');
    setRating(0);
    setMessage('');
    setEmail('');
    setIsSubmitting(false);
  };

  const handleClose = () => {
    setIsOpen(false);
    setTimeout(resetForm, 300);
  };

  return (
    <>
      {/* 反馈按钮 */}
      <button
        onClick={() => setIsOpen(true)}
        className="fixed bottom-20 right-4 z-40 bg-gradient-to-r from-purple-600 to-indigo-600 text-white p-3 rounded-full shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200"
        aria-label="提供反馈"
        title="提供反馈"
      >
        <MessageCircle className="w-6 h-6" />
      </button>

      {/* 反馈面板 */}
      <AnimatePresence>
        {isOpen && (
          <>
            {/* 背景遮罩 */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 bg-black bg-opacity-50 z-30"
              onClick={handleClose}
            />

            {/* 面板 */}
            <motion.div
              initial={{ opacity: 0, scale: 0.9, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.9, y: 20 }}
              className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-full max-w-md bg-white rounded-xl shadow-2xl z-40 max-h-[90vh] overflow-y-auto"
            >
              {/* 头部 */}
              <div className="flex items-center justify-between p-6 border-b border-gray-200">
                <h2 className="text-xl font-bold text-gray-800">
                  {step === 'success' ? '感谢您的反馈！' : '提供反馈'}
                </h2>
                <button
                  onClick={handleClose}
                  className="p-2 text-gray-500 hover:text-gray-700 rounded-lg hover:bg-gray-100 transition-colors"
                  aria-label="关闭"
                >
                  <X className="w-5 h-5" />
                </button>
              </div>

              <div className="p-6">
                {/* 选择反馈类型 */}
                {step === 'type' && (
                  <div className="space-y-4">
                    <p className="text-gray-600 mb-4">请选择反馈类型：</p>
                    {feedbackTypes.map((type) => (
                      <button
                        key={type.type}
                        onClick={() => {
                          setFeedbackType(type.type);
                          setStep('form');
                        }}
                        className={`w-full p-4 border-2 rounded-lg text-left hover:shadow-md transition-all duration-200 ${type.color}`}
                      >
                        <div className="flex items-start space-x-3">
                          <type.icon className="w-6 h-6 mt-1" />
                          <div>
                            <h3 className="font-semibold">{type.title}</h3>
                            <p className="text-sm opacity-80">{type.description}</p>
                          </div>
                        </div>
                      </button>
                    ))}
                  </div>
                )}

                {/* 反馈表单 */}
                {step === 'form' && (
                  <form onSubmit={handleSubmit} className="space-y-4">
                    {/* 评分 (仅评价类型) */}
                    {feedbackType === 'rating' && (
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          您对我们的服务评分如何？
                        </label>
                        <div className="flex space-x-1">
                          {[1, 2, 3, 4, 5].map((star) => (
                            <button
                              key={star}
                              type="button"
                              onClick={() => setRating(star)}
                              className={`p-1 rounded transition-colors ${
                                star <= rating ? 'text-yellow-400' : 'text-gray-300'
                              }`}
                            >
                              <Star className="w-8 h-8 fill-current" />
                            </button>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* 消息 */}
                    <div>
                      <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-2">
                        {feedbackType === 'bug' ? '请描述您遇到的问题：' :
                         feedbackType === 'suggestion' ? '请分享您的建议：' :
                         feedbackType === 'rating' ? '请告诉我们您的想法：' :
                         '请输入您的反馈：'}
                      </label>
                      <textarea
                        id="message"
                        value={message}
                        onChange={(e) => setMessage(e.target.value)}
                        rows={4}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 resize-none"
                        placeholder="请详细描述..."
                        required
                      />
                    </div>

                    {/* 邮箱 (可选) */}
                    <div>
                      <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                        邮箱地址 (可选)
                      </label>
                      <input
                        type="email"
                        id="email"
                        value={email}
                        onChange={(e) => setEmail(e.target.value)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500"
                        placeholder="如需回复请留下邮箱"
                      />
                    </div>

                    {/* 按钮 */}
                    <div className="flex space-x-3 pt-4">
                      <button
                        type="button"
                        onClick={() => setStep('type')}
                        className="flex-1 px-4 py-2 text-gray-700 bg-gray-100 border border-gray-300 rounded-lg hover:bg-gray-200 transition-colors"
                      >
                        返回
                      </button>
                      <button
                        type="submit"
                        disabled={isSubmitting || !message.trim()}
                        className="flex-1 flex items-center justify-center px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                      >
                        {isSubmitting ? (
                          <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
                        ) : (
                          <>
                            <Send className="w-4 h-4 mr-2" />
                            提交反馈
                          </>
                        )}
                      </button>
                    </div>
                  </form>
                )}

                {/* 成功页面 */}
                {step === 'success' && (
                  <div className="text-center py-8">
                    <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <Heart className="w-8 h-8 text-green-600" />
                    </div>
                    <h3 className="text-lg font-semibold text-gray-800 mb-2">
                      反馈已提交！
                    </h3>
                    <p className="text-gray-600 mb-6">
                      感谢您的宝贵意见，我们会认真考虑您的建议。
                    </p>
                    <button
                      onClick={handleClose}
                      className="px-6 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
                    >
                      关闭
                    </button>
                  </div>
                )}
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>
    </>
  );
};

export default FeedbackWidget;
