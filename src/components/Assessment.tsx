import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronLeft, ChevronRight, Save, Pause, Play, Shield, AlertTriangle, Home } from 'lucide-react';
import { useLanguage } from '../contexts/LanguageContext';
import { questions } from '../data/questions';
import { UserResponse, AssessmentResult, AssessmentSession } from '../types';
import { EnhancedProgressBar } from './EnhancedProgressBar';
import { LoadingSpinner } from './LoadingSpinner';
import { SecurityWarning } from './SecurityWarning';
import { BeforeUnloadHandler } from './BeforeUnloadHandler';
import { useAssessmentSecurity } from '../hooks/useAssessmentSecurity';
import { useAssessmentTimer } from '../hooks/useAssessmentTimer';
import { useKeyboardNavigation } from '../hooks/useKeyboardNavigation';
import { encryptData, decryptData, generateSessionId, getDeviceFingerprint } from '../utils/encryption';
import { trackAssessmentEvent } from './GoogleAnalytics';

interface AssessmentProps {
  onComplete: (result: AssessmentResult) => void;
  onBackToHome?: () => void;
}

export const Assessment: React.FC<AssessmentProps> = ({ onComplete, onBackToHome }) => {
  const { language, t } = useLanguage();
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [responses, setResponses] = useState<UserResponse[]>([]);
  const [selectedOption, setSelectedOption] = useState<number | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isPaused, setIsPaused] = useState(false);
  const [sessionId] = useState(() => generateSessionId());
  const [shuffledOptions, setShuffledOptions] = useState<number[]>([]);
  const [showSecurityWarning, setShowSecurityWarning] = useState(false);

  const { securityEvents, isSecure, warningCount } = useAssessmentSecurity();
  const { totalTime, questionTimes, startQuestionTimer, endQuestionTimer, formatTime } = useAssessmentTimer();

  // 选项随机排序
  useEffect(() => {
    const question = questions[currentQuestion];
    if (question) {
      const indices = Array.from({ length: question.options.length }, (_, i) => i);
      setShuffledOptions(indices.sort(() => Math.random() - 0.5));
    }
  }, [currentQuestion]);

  // 安全警告处理 - 更宽松的策略
  useEffect(() => {
    if (warningCount > 2) { // 从1改为2，给用户更多容错空间
      setShowSecurityWarning(true);
    }
  }, [warningCount]);

  // 加载保存的进度
  useEffect(() => {
    const savedProgress = localStorage.getItem('disc-assessment-progress');
    if (savedProgress) {
      try {
        const decryptedData = decryptData(savedProgress);
        if (decryptedData && 
            typeof decryptedData.currentQuestion === 'number' && 
            decryptedData.currentQuestion >= 0 && 
            decryptedData.currentQuestion < questions.length) {
          setCurrentQuestion(decryptedData.currentQuestion);
          setResponses(decryptedData.responses || []);
        } else {
          console.warn('Invalid saved progress data, resetting assessment');
          localStorage.removeItem('disc-assessment-progress');
          setCurrentQuestion(0);
          setResponses([]);
        }
      } catch (error) {
        console.error('Failed to load saved progress:', error);
        localStorage.removeItem('disc-assessment-progress');
        setCurrentQuestion(0);
        setResponses([]);
      }
    }
    startQuestionTimer();

    // 追踪评估开始事件
    trackAssessmentEvent.start();
  }, [startQuestionTimer]);

  // 保存进度
  const saveProgress = useCallback((questionIndex: number, currentResponses: UserResponse[]) => {
    if (questionIndex < 0 || questionIndex >= questions.length) {
      console.warn('Invalid question index, not saving progress');
      return;
    }

    const session: AssessmentSession = {
      id: sessionId,
      startTime: Date.now() - totalTime * 1000,
      currentQuestion: questionIndex,
      responses: currentResponses,
      isCompleted: false,
      deviceInfo: getDeviceFingerprint(),
      suspiciousActivity: securityEvents.map(e => e.type)
    };
    
    const encryptedData = encryptData(session);
    localStorage.setItem('disc-assessment-progress', encryptedData);
  }, [sessionId, totalTime, securityEvents]);

  const handleOptionSelect = useCallback((optionIndex: number) => {
    if (isPaused) return;
    
    // 触觉反馈
    if ('vibrate' in navigator) {
      navigator.vibrate(50);
    }
    
    setSelectedOption(optionIndex);
  }, [isPaused]);

  const handleNext = useCallback(() => {
    if (selectedOption === null || isPaused) return;

    const questionTime = endQuestionTimer();
    const question = questions[currentQuestion];
    
    if (!question) {
      console.error('Current question is undefined');
      return;
    }

    const actualOptionIndex = shuffledOptions[selectedOption];
    const option = question.options[actualOptionIndex];
    
    if (!option) {
      console.error('Selected option is undefined');
      return;
    }
    
    const newResponse: UserResponse = {
      questionId: question.id,
      selectedOption: actualOptionIndex,
      type: option.type,
      value: option.value,
      responseTime: questionTime,
      timestamp: Date.now()
    };

    const updatedResponses = [...responses];
    const existingIndex = updatedResponses.findIndex(r => r.questionId === question.id);
    
    if (existingIndex >= 0) {
      updatedResponses[existingIndex] = newResponse;
    } else {
      updatedResponses.push(newResponse);
    }

    setResponses(updatedResponses);
    
    if (currentQuestion < questions.length - 1) {
      const nextQuestion = currentQuestion + 1;
      setCurrentQuestion(nextQuestion);
      setSelectedOption(null);
      saveProgress(nextQuestion, updatedResponses);
      startQuestionTimer();
      
      const nextResponse = updatedResponses.find(r => r.questionId === questions[nextQuestion].id);
      if (nextResponse) {
        const nextShuffledIndex = shuffledOptions.findIndex(i => i === nextResponse.selectedOption);
        setSelectedOption(nextShuffledIndex);
      }
    } else {
      handleSubmit(updatedResponses);
    }
  }, [selectedOption, isPaused, currentQuestion, shuffledOptions, responses, endQuestionTimer, saveProgress, startQuestionTimer]);

  const handlePrevious = useCallback(() => {
    if (currentQuestion > 0 && !isPaused) {
      const prevQuestion = currentQuestion - 1;
      setCurrentQuestion(prevQuestion);
      
      const prevResponse = responses.find(r => r.questionId === questions[prevQuestion].id);
      if (prevResponse) {
        const prevShuffledIndex = shuffledOptions.findIndex(i => i === prevResponse.selectedOption);
        setSelectedOption(prevShuffledIndex);
      } else {
        setSelectedOption(null);
      }
      saveProgress(prevQuestion, responses);
      startQuestionTimer();
    }
  }, [currentQuestion, isPaused, responses, shuffledOptions, saveProgress, startQuestionTimer]);

  // 键盘导航
  useKeyboardNavigation({
    onOptionSelect: handleOptionSelect,
    onNext: handleNext,
    onPrevious: handlePrevious,
    selectedOption,
    optionsCount: questions[currentQuestion]?.options.length || 0,
    canGoNext: selectedOption !== null && !isPaused,
    canGoPrevious: currentQuestion > 0 && !isPaused
  });

  const calculateResults = (finalResponses: UserResponse[]): AssessmentResult => {
    const scores = { D: 0, I: 0, S: 0, C: 0 };
    
    finalResponses.forEach(response => {
      scores[response.type] += response.value;
    });

    const total = scores.D + scores.I + scores.S + scores.C;
    const percentages = {
      D: Math.round((scores.D / total) * 100),
      I: Math.round((scores.I / total) * 100),
      S: Math.round((scores.S / total) * 100),
      C: Math.round((scores.C / total) * 100)
    };

    const primaryType = Object.entries(scores).reduce((a, b) => 
      scores[a[0] as keyof typeof scores] > scores[b[0] as keyof typeof scores] ? a : b
    )[0] as 'D' | 'I' | 'S' | 'C';

    return {
      id: sessionId,
      scores,
      percentages,
      primaryType,
      completedAt: new Date().toISOString(),
      language,
      totalTime,
      questionTimes,
      sessionId
    };
  };

  const handleSubmit = async (finalResponses: UserResponse[]) => {
    // 移除严格的安全检查，允许正常完成
    setIsSubmitting(true);
    
    await new Promise(resolve => setTimeout(resolve, 1500));
    
    const result = calculateResults(finalResponses);
    
    const history = JSON.parse(localStorage.getItem('disc-assessment-history') || '[]');
    history.push(result);
    localStorage.setItem('disc-assessment-history', JSON.stringify(history));
    
    localStorage.removeItem('disc-assessment-progress');

    // 追踪评估完成事件
    trackAssessmentEvent.complete(`${result.primaryType}-${result.secondaryType}`);

    setIsSubmitting(false);
    onComplete(result);
  };

  const handlePause = () => {
    setIsPaused(!isPaused);
    if (!isPaused) {
      saveProgress(currentQuestion, responses);
    }
  };

  const handleSecurityWarningDismiss = () => {
    setShowSecurityWarning(false);
  };

  const handleSecurityTerminate = () => {
    localStorage.removeItem('disc-assessment-progress');
    if (onBackToHome) {
      onBackToHome();
    } else {
      window.location.href = '/';
    }
  };

  const handleBackToHome = () => {
    if (window.confirm(t('assessment.confirmExit'))) {
      localStorage.removeItem('disc-assessment-progress');
      if (onBackToHome) {
        onBackToHome();
      }
    }
  };

  const question = questions[currentQuestion];
  if (!question) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50 flex items-center justify-center">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="bg-white rounded-2xl shadow-xl p-8 text-center max-w-md mx-4"
        >
          <LoadingSpinner size="lg" className="mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-gray-800 mb-2">
            {t('common.loading')}
          </h3>
          <p className="text-gray-600">
            {t('assessment.loadingMessage')}
          </p>
        </motion.div>
      </div>
    );
  }

  // 只有在真正严重违规时才终止（warningCount >= 5）
  if (!isSecure && warningCount >= 5) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-red-50 via-white to-red-50 flex items-center justify-center">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="bg-white rounded-2xl shadow-xl p-8 text-center max-w-md mx-4 border-2 border-red-200"
        >
          <AlertTriangle className="w-16 h-16 text-red-500 mx-auto mb-4" />
          <h3 className="text-2xl font-bold text-red-700 mb-4">{t('assessment.terminated')}</h3>
          <p className="text-gray-700 mb-6">
            {t('assessment.terminatedMessage')}
          </p>
          <button
            onClick={handleSecurityTerminate}
            className="px-6 py-3 bg-red-600 text-white rounded-lg font-medium hover:bg-red-700 transition-colors"
          >
            {t('common.returnHome')}
          </button>
        </motion.div>
      </div>
    );
  }

  if (isSubmitting) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50 flex items-center justify-center">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          className="bg-white rounded-2xl shadow-xl p-8 text-center max-w-md mx-4"
        >
          <LoadingSpinner size="lg" className="mx-auto mb-4" />
          <h3 className="text-xl font-semibold text-gray-800 mb-2">
            {t('common.loading')}
          </h3>
          <p className="text-gray-600">
            {t('assessment.processingResults')}
          </p>
        </motion.div>
      </div>
    );
  }

  return (
    <>
      <BeforeUnloadHandler isActive={true} />
      
      <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50 py-4 px-4">
        <div className="max-w-4xl mx-auto">
          {/* Header with Security Status and Home Button */}
          <div className="text-center mb-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-4">
                <button
                  onClick={handleBackToHome}
                  className="flex items-center space-x-2 px-3 py-2 rounded-lg bg-gray-100 text-gray-700 font-medium hover:bg-gray-200 transition-all duration-200"
                  title={t('common.returnHome')}
                >
                  <Home className="w-4 h-4" />
                  <span className="hidden sm:inline">{t('common.home')}</span>
                </button>
                
                <div className="flex items-center space-x-2">
                  <Shield className={`w-5 h-5 ${isSecure ? 'text-green-500' : 'text-yellow-500'}`} />
                  <span className={`text-sm font-medium ${isSecure ? 'text-green-600' : 'text-yellow-600'}`}>
                    {isSecure ? t('assessment.secureSession') : `${t('assessment.warnings')}: ${warningCount}/5`}
                  </span>
                </div>
              </div>
              
              <button
                onClick={handlePause}
                className="flex items-center space-x-2 px-3 py-2 rounded-lg bg-amber-100 text-amber-700 font-medium hover:bg-amber-200 transition-all duration-200"
              >
                {isPaused ? <Play className="w-4 h-4" /> : <Pause className="w-4 h-4" />}
                <span className="hidden sm:inline">{isPaused ? t('common.resume') : t('common.pause')}</span>
              </button>
            </div>

            <h1 className="text-2xl md:text-3xl font-bold text-gray-800 mb-2">
              {t('assessment.title')}
            </h1>
            <p className="text-gray-600 mb-4">
              {t('assessment.instruction')}
            </p>
            <EnhancedProgressBar 
              current={currentQuestion + 1} 
              total={questions.length}
              totalTime={totalTime}
            />
          </div>

          {/* Question Card */}
          <AnimatePresence mode="wait">
            <motion.div
              key={currentQuestion}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.3 }}
              className={`bg-white rounded-2xl shadow-xl p-6 md:p-8 mb-6 ${isPaused ? 'opacity-50 pointer-events-none' : ''}`}
            >
              <div className="mb-6">
                <div className="text-sm font-medium text-indigo-600 mb-2">
                  {t('assessment.questionOf')
                    .replace('{{current}}', (currentQuestion + 1).toString())
                    .replace('{{total}}', questions.length.toString())}
                </div>
                <h2 className="text-lg md:text-xl font-semibold text-gray-800">
                  {question.text[language]}
                </h2>
              </div>

              <div className="space-y-3">
                {shuffledOptions.map((originalIndex, displayIndex) => {
                  const option = question.options[originalIndex];
                  if (!option) return null;
                  
                  return (
                    <motion.button
                      key={originalIndex}
                      onClick={() => handleOptionSelect(displayIndex)}
                      className={`w-full p-4 md:p-6 text-left rounded-xl border-2 transition-all duration-200 touch-manipulation ${
                        selectedOption === displayIndex
                          ? 'border-indigo-500 bg-indigo-50 text-indigo-700 shadow-md scale-[1.02]'
                          : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50 hover:shadow-sm'
                      }`}
                      whileHover={{ scale: selectedOption === displayIndex ? 1.02 : 1.01 }}
                      whileTap={{ scale: 0.98 }}
                      style={{ minHeight: '60px', minWidth: '44px' }}
                    >
                      <div className="flex items-center space-x-3">
                        <div className="flex-shrink-0">
                          <div className={`w-6 h-6 rounded-full border-2 flex items-center justify-center transition-all duration-200 ${
                            selectedOption === displayIndex
                              ? 'border-indigo-500 bg-indigo-500'
                              : 'border-gray-300'
                          }`}>
                            {selectedOption === displayIndex && (
                              <motion.div
                                initial={{ scale: 0 }}
                                animate={{ scale: 1 }}
                                className="w-2 h-2 bg-white rounded-full"
                              />
                            )}
                          </div>
                        </div>
                        <div className="flex-1">
                          <span className="font-medium text-sm md:text-base">
                            {displayIndex + 1}. {option.text[language]}
                          </span>
                        </div>
                      </div>
                    </motion.button>
                  );
                })}
              </div>

              <div className="mt-6 p-3 bg-gray-50 rounded-lg">
                <p className="text-xs text-gray-600 text-center">
                  💡 {t('assessment.keyboardHint')}
                </p>
              </div>
            </motion.div>
          </AnimatePresence>

          {/* Navigation */}
          <div className="flex justify-between items-center">
            <button
              onClick={handlePrevious}
              disabled={currentQuestion === 0 || isPaused}
              className="flex items-center space-x-2 px-4 md:px-6 py-3 rounded-xl bg-gray-100 text-gray-600 font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-200 touch-manipulation"
              style={{ minHeight: '44px' }}
            >
              <ChevronLeft className="w-5 h-5" />
              <span>{t('common.previous')}</span>
            </button>

            <button
              onClick={() => saveProgress(currentQuestion, responses)}
              disabled={isPaused}
              className="flex items-center space-x-2 px-3 md:px-4 py-2 rounded-lg bg-amber-100 text-amber-700 font-medium hover:bg-amber-200 transition-all duration-200 disabled:opacity-50 touch-manipulation"
              title={t('common.saveProgress')}
            >
              <Save className="w-4 h-4" />
              <span className="hidden sm:inline">{t('common.save')}</span>
            </button>

            <motion.button
              onClick={handleNext}
              disabled={selectedOption === null || isPaused}
              className="flex items-center space-x-2 px-4 md:px-6 py-3 rounded-xl bg-indigo-600 text-white font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed hover:bg-indigo-700 touch-manipulation"
              whileHover={{ scale: selectedOption !== null ? 1.05 : 1 }}
              whileTap={{ scale: 0.95 }}
              style={{ minHeight: '44px' }}
            >
              <span>
                {currentQuestion === questions.length - 1 ? t('common.submit') : t('common.next')}
              </span>
              <ChevronRight className="w-5 h-5" />
            </motion.button>
          </div>
        </div>
      </div>

      {/* Security Warning Modal */}
      <SecurityWarning
        warningCount={warningCount}
        isVisible={showSecurityWarning}
        onDismiss={handleSecurityWarningDismiss}
        onTerminate={handleSecurityTerminate}
      />
    </>
  );
};