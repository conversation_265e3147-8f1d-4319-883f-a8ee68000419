import { useEffect } from 'react';

interface BeforeUnloadHandlerProps {
  isActive: boolean;
  message?: string;
}

export const BeforeUnloadHandler: React.FC<BeforeUnloadHandlerProps> = ({ 
  isActive, 
  message = 'Are you sure you want to leave? Your progress will be lost.' 
}) => {
  useEffect(() => {
    if (!isActive) return;

    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      e.preventDefault();
      e.returnValue = message;
      return message;
    };

    const handlePopState = (e: PopStateEvent) => {
      if (window.confirm(message)) {
        return;
      } else {
        window.history.pushState(null, '', window.location.href);
      }
    };

    // 添加浏览器返回拦截
    window.history.pushState(null, '', window.location.href);
    
    window.addEventListener('beforeunload', handleBeforeUnload);
    window.addEventListener('popstate', handlePopState);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      window.removeEventListener('popstate', handlePopState);
    };
  }, [isActive, message]);

  return null;
};