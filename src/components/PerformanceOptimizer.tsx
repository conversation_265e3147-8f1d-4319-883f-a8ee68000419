import React, { useEffect } from 'react';
import { Helmet } from 'react-helmet-async';

// 性能优化组件
export const PerformanceOptimizer: React.FC = () => {
  useEffect(() => {
    // 预加载关键资源
    const preloadCriticalResources = () => {
      // 预加载字体
      const fontLink = document.createElement('link');
      fontLink.rel = 'preload';
      fontLink.href = '/fonts/inter-var.woff2';
      fontLink.as = 'font';
      fontLink.type = 'font/woff2';
      fontLink.crossOrigin = 'anonymous';
      document.head.appendChild(fontLink);

      // 预加载关键图片
      const heroImage = new Image();
      heroImage.src = '/images/hero-bg.jpg';
      
      const logoImage = new Image();
      logoImage.src = '/logo.png';
    };

    // 延迟加载非关键资源
    const lazyLoadResources = () => {
      // 延迟加载第三方脚本
      setTimeout(() => {
        // 这里可以加载非关键的第三方脚本
      }, 3000);
    };

    // 优化图片加载
    const optimizeImages = () => {
      const images = document.querySelectorAll('img[data-src]');
      const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const img = entry.target as HTMLImageElement;
            img.src = img.dataset.src || '';
            img.classList.remove('lazy');
            observer.unobserve(img);
          }
        });
      });

      images.forEach(img => imageObserver.observe(img));
    };

    // 预连接到外部域名
    const preconnectExternalDomains = () => {
      const domains = [
        'https://fonts.googleapis.com',
        'https://fonts.gstatic.com',
        'https://www.googletagmanager.com',
        'https://www.google-analytics.com'
      ];

      domains.forEach(domain => {
        const link = document.createElement('link');
        link.rel = 'preconnect';
        link.href = domain;
        if (domain.includes('gstatic')) {
          link.crossOrigin = 'anonymous';
        }
        document.head.appendChild(link);
      });
    };

    // 监控性能指标
    const monitorPerformance = () => {
      // Core Web Vitals 监控
      if ('web-vital' in window) {
        // 这里可以集成 web-vitals 库
      }

      // 监控资源加载时间
      if ('PerformanceObserver' in window) {
        const observer = new PerformanceObserver((list) => {
          list.getEntries().forEach((entry) => {
            if (entry.entryType === 'navigation') {
              const navEntry = entry as PerformanceNavigationTiming;
              console.log('Page Load Time:', navEntry.loadEventEnd - navEntry.fetchStart);
            }
          });
        });
        observer.observe({ entryTypes: ['navigation'] });
      }
    };

    // 执行优化
    preloadCriticalResources();
    lazyLoadResources();
    optimizeImages();
    preconnectExternalDomains();
    monitorPerformance();

    // 清理函数
    return () => {
      // 清理观察者等
    };
  }, []);

  return (
    <Helmet>
      {/* 关键资源预加载 */}
      <link rel="preload" href="/fonts/inter-var.woff2" as="font" type="font/woff2" crossOrigin="anonymous" />
      <link rel="preload" href="/images/hero-bg.jpg" as="image" />
      <link rel="preload" href="/logo.png" as="image" />
      
      {/* DNS 预解析 */}
      <link rel="dns-prefetch" href="//fonts.googleapis.com" />
      <link rel="dns-prefetch" href="//fonts.gstatic.com" />
      <link rel="dns-prefetch" href="//www.googletagmanager.com" />
      <link rel="dns-prefetch" href="//www.google-analytics.com" />
      
      {/* 预连接到关键第三方域名 */}
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      
      {/* 资源提示 */}
      <link rel="prefetch" href="/assessment" />
      <link rel="prefetch" href="/about" />
      <link rel="prefetch" href="/blog" />
      
      {/* 性能优化 meta 标签 */}
      <meta httpEquiv="x-dns-prefetch-control" content="on" />
      
      {/* 移动端优化 */}
      <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover" />
      <meta name="mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-status-bar-style" content="default" />
      
      {/* PWA 相关 */}
      <link rel="manifest" href="/manifest.json" />
      <meta name="theme-color" content="#4F46E5" />
      
      {/* 图标预加载 */}
      <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
      <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
      <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    </Helmet>
  );
};

// 懒加载图片组件
interface LazyImageProps {
  src: string;
  alt: string;
  className?: string;
  placeholder?: string;
}

export const LazyImage: React.FC<LazyImageProps> = ({ 
  src, 
  alt, 
  className = '', 
  placeholder = '/images/placeholder.jpg' 
}) => {
  const [isLoaded, setIsLoaded] = React.useState(false);
  const [isInView, setIsInView] = React.useState(false);
  const imgRef = React.useRef<HTMLImageElement>(null);

  React.useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsInView(true);
          observer.disconnect();
        }
      },
      { threshold: 0.1 }
    );

    if (imgRef.current) {
      observer.observe(imgRef.current);
    }

    return () => observer.disconnect();
  }, []);

  return (
    <div className={`relative overflow-hidden ${className}`}>
      <img
        ref={imgRef}
        src={isInView ? src : placeholder}
        alt={alt}
        className={`transition-opacity duration-300 ${isLoaded ? 'opacity-100' : 'opacity-0'}`}
        onLoad={() => setIsLoaded(true)}
        loading="lazy"
      />
      {!isLoaded && (
        <div className="absolute inset-0 bg-gray-200 animate-pulse" />
      )}
    </div>
  );
};

// 代码分割和动态导入辅助函数
export const loadComponent = (importFunc: () => Promise<any>) => {
  return React.lazy(() => 
    importFunc().then(module => ({
      default: module.default || module
    }))
  );
};

// 性能监控 Hook
export const usePerformanceMonitoring = () => {
  React.useEffect(() => {
    // 监控 Core Web Vitals
    const reportWebVitals = (metric: any) => {
      // 发送到Google Analytics
      if (typeof window.gtag !== 'undefined') {
        window.gtag('event', metric.name, {
          event_category: 'Web Vitals',
          event_label: metric.id,
          value: Math.round(metric.value),
          non_interaction: true
        });
      }

      // 发送到控制台（开发环境）
      if (process.env.NODE_ENV === 'development') {
        console.log('Web Vital:', metric);
      }
    };

    // 监控 LCP (Largest Contentful Paint)
    if ('PerformanceObserver' in window) {
      const lcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1];
        reportWebVitals({
          name: 'LCP',
          value: lastEntry.startTime,
          id: 'lcp',
          rating: lastEntry.startTime <= 2500 ? 'good' : lastEntry.startTime <= 4000 ? 'needs-improvement' : 'poor'
        });
      });
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
    }

    // 监控 FID (First Input Delay)
    if ('PerformanceObserver' in window) {
      const fidObserver = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          const fid = (entry as any).processingStart - entry.startTime;
          reportWebVitals({
            name: 'FID',
            value: fid,
            id: 'fid',
            rating: fid <= 100 ? 'good' : fid <= 300 ? 'needs-improvement' : 'poor'
          });
        });
      });
      fidObserver.observe({ entryTypes: ['first-input'] });
    }

    // 监控 CLS (Cumulative Layout Shift)
    if ('PerformanceObserver' in window) {
      let clsValue = 0;
      const clsObserver = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          if (!(entry as any).hadRecentInput) {
            clsValue += (entry as any).value;
            reportWebVitals({
              name: 'CLS',
              value: clsValue,
              id: 'cls',
              rating: clsValue <= 0.1 ? 'good' : clsValue <= 0.25 ? 'needs-improvement' : 'poor'
            });
          }
        });
      });
      clsObserver.observe({ entryTypes: ['layout-shift'] });
    }

    // 监控 TTFB (Time to First Byte)
    if ('PerformanceObserver' in window) {
      const navigationObserver = new PerformanceObserver((list) => {
        list.getEntries().forEach((entry) => {
          const navEntry = entry as PerformanceNavigationTiming;
          const ttfb = navEntry.responseStart - navEntry.fetchStart;
          reportWebVitals({
            name: 'TTFB',
            value: ttfb,
            id: 'ttfb',
            rating: ttfb <= 800 ? 'good' : ttfb <= 1800 ? 'needs-improvement' : 'poor'
          });
        });
      });
      navigationObserver.observe({ entryTypes: ['navigation'] });
    }

    // 监控资源加载性能
    const monitorResourcePerformance = () => {
      if ('PerformanceObserver' in window) {
        const resourceObserver = new PerformanceObserver((list) => {
          list.getEntries().forEach((entry) => {
            const resource = entry as PerformanceResourceTiming;
            if (resource.duration > 1000) { // 超过1秒的资源
              console.warn('Slow resource:', {
                name: resource.name,
                duration: resource.duration,
                size: resource.transferSize
              });
            }
          });
        });
        resourceObserver.observe({ entryTypes: ['resource'] });
      }
    };

    // 监控内存使用
    const monitorMemoryUsage = () => {
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        setInterval(() => {
          const memoryInfo = {
            used: memory.usedJSHeapSize,
            total: memory.totalJSHeapSize,
            limit: memory.jsHeapSizeLimit
          };

          // 如果内存使用超过80%，发出警告
          if (memoryInfo.used / memoryInfo.limit > 0.8) {
            console.warn('High memory usage:', memoryInfo);
          }
        }, 30000); // 每30秒检查一次
      }
    };

    monitorResourcePerformance();
    monitorMemoryUsage();
  }, []);
};

export default PerformanceOptimizer;
