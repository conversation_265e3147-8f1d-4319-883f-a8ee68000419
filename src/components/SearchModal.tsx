import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Search, X, FileText, HelpCircle, BookOpen, User } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useLanguage } from '../contexts/LanguageContext';

interface SearchResult {
  id: string;
  title: string;
  description: string;
  url: string;
  type: 'page' | 'blog' | 'faq' | 'type';
  icon: React.ComponentType<any>;
}

interface SearchModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export const SearchModal: React.FC<SearchModalProps> = ({ isOpen, onClose }) => {
  const { t } = useLanguage();
  const navigate = useNavigate();
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const inputRef = useRef<HTMLInputElement>(null);

  // 搜索数据
  const searchData: SearchResult[] = [
    {
      id: 'home',
      title: '首页',
      description: 'DISC人格评估主页',
      url: '/',
      type: 'page',
      icon: FileText
    },
    {
      id: 'assessment',
      title: 'DISC评估',
      description: '开始免费的DISC人格评估',
      url: '/assessment',
      type: 'page',
      icon: FileText
    },
    {
      id: 'about',
      title: '关于DISC',
      description: '了解DISC评估的科学基础',
      url: '/about',
      type: 'page',
      icon: FileText
    },
    {
      id: 'blog',
      title: '博客',
      description: 'DISC相关文章和见解',
      url: '/blog',
      type: 'page',
      icon: BookOpen
    },
    {
      id: 'faq',
      title: '常见问题',
      description: 'DISC评估常见问题解答',
      url: '/faq',
      type: 'page',
      icon: HelpCircle
    },
    {
      id: 'contact',
      title: '联系我们',
      description: '获取支持和帮助',
      url: '/contact',
      type: 'page',
      icon: FileText
    },
    {
      id: 'type-d',
      title: 'D型人格 - 支配型',
      description: '了解支配型人格特征和特点',
      url: '/about#type-d',
      type: 'type',
      icon: User
    },
    {
      id: 'type-i',
      title: 'I型人格 - 影响型',
      description: '了解影响型人格特征和特点',
      url: '/about#type-i',
      type: 'type',
      icon: User
    },
    {
      id: 'type-s',
      title: 'S型人格 - 稳定型',
      description: '了解稳定型人格特征和特点',
      url: '/about#type-s',
      type: 'type',
      icon: User
    },
    {
      id: 'type-c',
      title: 'C型人格 - 谨慎型',
      description: '了解谨慎型人格特征和特点',
      url: '/about#type-c',
      type: 'type',
      icon: User
    }
  ];

  // 搜索功能
  useEffect(() => {
    if (!query.trim()) {
      setResults([]);
      setSelectedIndex(0);
      return;
    }

    const filtered = searchData.filter(item =>
      item.title.toLowerCase().includes(query.toLowerCase()) ||
      item.description.toLowerCase().includes(query.toLowerCase())
    );

    setResults(filtered);
    setSelectedIndex(0);
  }, [query]);

  // 键盘导航
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isOpen) return;

      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          setSelectedIndex(prev => 
            prev < results.length - 1 ? prev + 1 : prev
          );
          break;
        case 'ArrowUp':
          e.preventDefault();
          setSelectedIndex(prev => prev > 0 ? prev - 1 : prev);
          break;
        case 'Enter':
          e.preventDefault();
          if (results[selectedIndex]) {
            handleSelect(results[selectedIndex]);
          }
          break;
        case 'Escape':
          e.preventDefault();
          onClose();
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, results, selectedIndex, onClose]);

  // 自动聚焦
  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isOpen]);

  const handleSelect = (result: SearchResult) => {
    navigate(result.url);
    onClose();
    setQuery('');
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'page': return 'text-blue-600 bg-blue-50';
      case 'blog': return 'text-green-600 bg-green-50';
      case 'faq': return 'text-yellow-600 bg-yellow-50';
      case 'type': return 'text-purple-600 bg-purple-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'page': return '页面';
      case 'blog': return '博客';
      case 'faq': return '问答';
      case 'type': return '人格类型';
      default: return '其他';
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* 背景遮罩 */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black bg-opacity-50 z-50"
            onClick={onClose}
          />

          {/* 搜索模态框 */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: -20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: -20 }}
            className="fixed top-20 left-1/2 transform -translate-x-1/2 w-full max-w-2xl bg-white rounded-xl shadow-2xl z-50 overflow-hidden"
          >
            {/* 搜索输入 */}
            <div className="flex items-center p-4 border-b border-gray-200">
              <Search className="w-5 h-5 text-gray-400 mr-3" />
              <input
                ref={inputRef}
                type="text"
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                placeholder="搜索页面、文章、问题..."
                className="flex-1 text-lg outline-none"
              />
              <button
                onClick={onClose}
                className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-colors"
                aria-label="关闭搜索"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            {/* 搜索结果 */}
            <div className="max-h-96 overflow-y-auto">
              {query && results.length === 0 && (
                <div className="p-8 text-center text-gray-500">
                  <Search className="w-12 h-12 mx-auto mb-4 opacity-50" />
                  <p>没有找到相关结果</p>
                  <p className="text-sm mt-2">尝试使用不同的关键词</p>
                </div>
              )}

              {results.map((result, index) => (
                <button
                  key={result.id}
                  onClick={() => handleSelect(result)}
                  className={`w-full p-4 text-left hover:bg-gray-50 border-b border-gray-100 last:border-b-0 transition-colors ${
                    index === selectedIndex ? 'bg-indigo-50 border-indigo-200' : ''
                  }`}
                >
                  <div className="flex items-start space-x-3">
                    <div className={`p-2 rounded-lg ${getTypeColor(result.type)}`}>
                      <result.icon className="w-4 h-4" />
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2 mb-1">
                        <h3 className="font-medium text-gray-900 truncate">
                          {result.title}
                        </h3>
                        <span className={`px-2 py-1 text-xs rounded-full ${getTypeColor(result.type)}`}>
                          {getTypeLabel(result.type)}
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 line-clamp-2">
                        {result.description}
                      </p>
                    </div>
                  </div>
                </button>
              ))}
            </div>

            {/* 快捷键提示 */}
            {!query && (
              <div className="p-4 bg-gray-50 border-t border-gray-200">
                <div className="flex items-center justify-between text-xs text-gray-500">
                  <div className="flex items-center space-x-4">
                    <span className="flex items-center">
                      <kbd className="px-2 py-1 bg-white border border-gray-300 rounded text-xs">↑↓</kbd>
                      <span className="ml-1">导航</span>
                    </span>
                    <span className="flex items-center">
                      <kbd className="px-2 py-1 bg-white border border-gray-300 rounded text-xs">Enter</kbd>
                      <span className="ml-1">选择</span>
                    </span>
                    <span className="flex items-center">
                      <kbd className="px-2 py-1 bg-white border border-gray-300 rounded text-xs">Esc</kbd>
                      <span className="ml-1">关闭</span>
                    </span>
                  </div>
                </div>
              </div>
            )}
          </motion.div>
        </>
      )}
    </AnimatePresence>
  );
};

// 搜索触发器组件
export const SearchTrigger: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);

  // 键盘快捷键
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Cmd/Ctrl + K 打开搜索
      if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
        e.preventDefault();
        setIsOpen(true);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);

  return (
    <>
      <button
        onClick={() => setIsOpen(true)}
        className="flex items-center space-x-2 px-3 py-2 text-gray-600 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors"
        aria-label="搜索"
      >
        <Search className="w-4 h-4" />
        <span className="text-sm">搜索...</span>
        <kbd className="hidden sm:inline-block px-2 py-1 text-xs bg-white border border-gray-300 rounded">
          ⌘K
        </kbd>
      </button>

      <SearchModal isOpen={isOpen} onClose={() => setIsOpen(false)} />
    </>
  );
};

export default SearchModal;
