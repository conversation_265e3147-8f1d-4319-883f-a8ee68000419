import React from 'react';
import { motion } from 'framer-motion';
import { Download, Share2, RotateCcw, Award, TrendingUp, MessageCircle, Briefcase, Clock, BarChart3, Home } from 'lucide-react';
import { useLanguage } from '../contexts/LanguageContext';
import { AssessmentResult } from '../types';
import { calculateAnalytics, generateDetailedReport } from '../utils/analytics';
import { trackAssessmentEvent } from './GoogleAnalytics';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';

interface ResultsProps {
  result: AssessmentResult;
  onRetake: () => void;
  onBackToHome?: () => void;
}

export const Results: React.FC<ResultsProps> = ({ result, onRetake, onBackToHome }) => {
  const { language, t } = useLanguage();
  const primaryType = result.primaryType;

  const analytics = calculateAnalytics([], result.questionTimes || []);
  const detailedReport = generateDetailedReport(result, analytics);

  const handleRetake = () => {
    trackAssessmentEvent.retake();
    onRetake();
  };

  const typeColors = {
    D: { bg: 'bg-red-100', text: 'text-red-700', border: 'border-red-200', accent: 'bg-red-500' },
    I: { bg: 'bg-yellow-100', text: 'text-yellow-700', border: 'border-yellow-200', accent: 'bg-yellow-500' },
    S: { bg: 'bg-green-100', text: 'text-green-700', border: 'border-green-200', accent: 'bg-green-500' },
    C: { bg: 'bg-blue-100', text: 'text-blue-700', border: 'border-blue-200', accent: 'bg-blue-500' }
  };

  const generateEnhancedPDF = async () => {
    try {
      const element = document.getElementById('results-content');
      if (!element) return;

      const canvas = await html2canvas(element, {
        scale: 2,
        useCORS: true,
        backgroundColor: '#ffffff'
      });

      const imgData = canvas.toDataURL('image/png');
      const pdf = new jsPDF('p', 'mm', 'a4');
      
      // Add header
      pdf.setFontSize(20);
      pdf.text(t('results.pdfTitle'), 20, 20);
      
      // Add timestamp
      pdf.setFontSize(10);
      pdf.text(`${t('results.generated')}: ${new Date().toLocaleString()}`, 20, 30);
      
      // Add main content
      const imgWidth = 170;
      const pageHeight = 250;
      const imgHeight = (canvas.height * imgWidth) / canvas.width;
      let heightLeft = imgHeight;
      let position = 40;

      pdf.addImage(imgData, 'PNG', 20, position, imgWidth, imgHeight);
      heightLeft -= pageHeight;

      while (heightLeft >= 0) {
        position = heightLeft - imgHeight + 40;
        pdf.addPage();
        pdf.addImage(imgData, 'PNG', 20, position, imgWidth, imgHeight);
        heightLeft -= pageHeight;
      }

      // Add analytics page
      pdf.addPage();
      pdf.setFontSize(16);
      pdf.text(t('results.analytics'), 20, 20);
      
      pdf.setFontSize(12);
      let yPos = 40;
      
      pdf.text(`${t('results.totalTime')}: ${Math.floor(result.totalTime / 60)}:${(result.totalTime % 60).toString().padStart(2, '0')}`, 20, yPos);
      yPos += 10;
      
      if (analytics.averageTimePerQuestion) {
        pdf.text(`${t('results.avgTimePerQuestion')}: ${analytics.averageTimePerQuestion}s`, 20, yPos);
        yPos += 10;
      }
      
      pdf.text(`${t('results.consistencyScore')}: ${analytics.consistencyScore}%`, 20, yPos);
      yPos += 10;
      
      pdf.text(`${t('results.confidenceLevel')}: ${analytics.confidenceLevel.toUpperCase()}`, 20, yPos);
      yPos += 20;
      
      pdf.text(`${t('results.recommendations')}:`, 20, yPos);
      yPos += 10;
      
      detailedReport.insights.recommendations.forEach((rec: string, index: number) => {
        const translatedRec = t(`results.personalizedRecommendations.${rec}`) || rec;
        pdf.text(`${index + 1}. ${translatedRec}`, 25, yPos);
        yPos += 8;
      });

      pdf.save(`disc-assessment-${result.id}.pdf`);

      // 追踪PDF下载事件
      trackAssessmentEvent.downloadPDF();
    } catch (error) {
      console.error('PDF generation failed:', error);
      alert(t('results.pdfError'));
    }
  };

  const shareResults = async () => {
    try {
      const shareData = {
        title: t('results.title'),
        text: t('results.shareText')
          .replace('{{type}}', t(`types.${primaryType}.name`))
          .replace('{{time}}', `${Math.floor(result.totalTime / 60)}:${(result.totalTime % 60).toString().padStart(2, '0')}`),
        url: window.location.href
      };

      if (navigator.share && navigator.canShare && navigator.canShare(shareData)) {
        await navigator.share(shareData);
        trackAssessmentEvent.shareResult('native_share');
      } else {
        // Fallback to clipboard
        const textToShare = `${shareData.text}\n\n${shareData.url}`;
        await navigator.clipboard.writeText(textToShare);
        alert(t('results.copiedToClipboard'));
        trackAssessmentEvent.shareResult('clipboard');
      }
    } catch (error) {
      console.error('Sharing failed:', error);
      // Final fallback
      const fallbackText = `${t('results.shareText')
        .replace('{{type}}', t(`types.${primaryType}.name`))
        .replace('{{time}}', `${Math.floor(result.totalTime / 60)}:${(result.totalTime % 60).toString().padStart(2, '0')}`)} ${window.location.href}`;
      
      try {
        await navigator.clipboard.writeText(fallbackText);
        alert(t('results.copiedToClipboard'));
      } catch (clipboardError) {
        console.error('Clipboard fallback failed:', clipboardError);
        alert(t('results.shareError'));
      }
    }
  };

  const exportData = () => {
    try {
      const exportData = {
        result,
        analytics,
        detailedReport,
        exportedAt: new Date().toISOString()
      };
      
      const dataStr = JSON.stringify(exportData, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);
      
      const link = document.createElement('a');
      link.href = url;
      link.download = `disc-assessment-data-${result.id}.json`;
      link.click();
      
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Data export failed:', error);
      alert(t('results.exportError'));
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50 py-8 px-4">
      <div className="max-w-4xl mx-auto">
        {/* Back to Home Button */}
        {onBackToHome && (
          <div className="mb-6">
            <button
              onClick={onBackToHome}
              className="flex items-center space-x-2 px-4 py-2 rounded-lg bg-gray-100 text-gray-700 font-medium hover:bg-gray-200 transition-all duration-200"
            >
              <Home className="w-4 h-4" />
              <span>{t('common.returnHome')}</span>
            </button>
          </div>
        )}

        <div id="results-content" className="space-y-8">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center"
          >
            <h1 className="text-4xl font-bold text-gray-800 mb-4">
              {t('results.title')}
            </h1>
            <div className="flex items-center justify-center space-x-6 text-sm text-gray-600">
              <div className="flex items-center space-x-1">
                <Clock className="w-4 h-4" />
                <span>{t('results.completed')}: {new Date(result.completedAt).toLocaleDateString()}</span>
              </div>
              <div className="flex items-center space-x-1">
                <BarChart3 className="w-4 h-4" />
                <span>{t('results.time')}: {formatTime(result.totalTime)}</span>
              </div>
            </div>
          </motion.div>

          {/* Primary Type Card */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.2 }}
            className={`${typeColors[primaryType].bg} ${typeColors[primaryType].border} border-2 rounded-2xl p-8`}
          >
            <div className="text-center">
              <div className={`inline-flex items-center justify-center w-16 h-16 ${typeColors[primaryType].accent} text-white rounded-full text-2xl font-bold mb-4`}>
                {primaryType}
              </div>
              <h2 className={`text-3xl font-bold ${typeColors[primaryType].text} mb-2`}>
                {t(`types.${primaryType}.name`)}
              </h2>
              <p className={`text-lg ${typeColors[primaryType].text} opacity-80`}>
                {t(`types.${primaryType}.shortDesc`)}
              </p>
            </div>
          </motion.div>

          {/* Performance Analytics */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="bg-white rounded-2xl shadow-xl p-8"
          >
            <h3 className="text-2xl font-bold text-gray-800 mb-6 flex items-center">
              <BarChart3 className="w-6 h-6 mr-2 text-indigo-600" />
              {t('results.analytics')}
            </h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-indigo-600 mb-1">
                  {formatTime(result.totalTime)}
                </div>
                <div className="text-sm text-gray-600">{t('results.totalTime')}</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600 mb-1">
                  {analytics.averageTimePerQuestion}s
                </div>
                <div className="text-sm text-gray-600">{t('results.avgPerQuestion')}</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600 mb-1">
                  {analytics.consistencyScore}%
                </div>
                <div className="text-sm text-gray-600">{t('results.consistency')}</div>
              </div>
              <div className="text-center">
                <div className={`text-2xl font-bold mb-1 ${
                  analytics.confidenceLevel === 'high' ? 'text-green-600' :
                  analytics.confidenceLevel === 'medium' ? 'text-yellow-600' : 'text-red-600'
                }`}>
                  {t(`results.confidence.${analytics.confidenceLevel}`)}
                </div>
                <div className="text-sm text-gray-600">{t('results.confidence.label')}</div>
              </div>
            </div>
          </motion.div>

          {/* Personality Breakdown */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="bg-white rounded-2xl shadow-xl p-8"
          >
            <h3 className="text-2xl font-bold text-gray-800 mb-6 flex items-center">
              <TrendingUp className="w-6 h-6 mr-2 text-indigo-600" />
              {t('results.breakdown')}
            </h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
              {(['D', 'I', 'S', 'C'] as const).map((type, index) => (
                <motion.div
                  key={type}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.5 + index * 0.1 }}
                  className="text-center"
                >
                  <div className={`w-20 h-20 mx-auto mb-3 rounded-full ${typeColors[type].accent} text-white flex items-center justify-center relative overflow-hidden`}>
                    <motion.div
                      className="absolute bottom-0 left-0 right-0 bg-white/30"
                      initial={{ height: 0 }}
                      animate={{ height: `${result.percentages[type]}%` }}
                      transition={{ duration: 1, delay: 0.8 + index * 0.1 }}
                    />
                    <span className="text-lg font-bold relative z-10">{type}</span>
                  </div>
                  <div className="text-2xl font-bold text-gray-800 mb-1">
                    {result.percentages[type]}%
                  </div>
                  <div className="text-sm text-gray-600">
                    {t(`types.${type}.name`)}
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Detailed Information */}
          <div className="grid md:grid-cols-2 gap-8">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.6 }}
              className="bg-white rounded-2xl shadow-xl p-6"
            >
              <h4 className="text-xl font-bold text-gray-800 mb-4 flex items-center">
                <Award className="w-5 h-5 mr-2 text-indigo-600" />
                {t('results.description')}
              </h4>
              <p className="text-gray-700 leading-relaxed">
                {t(`types.${primaryType}.description`)}
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.7 }}
              className="bg-white rounded-2xl shadow-xl p-6"
            >
              <h4 className="text-xl font-bold text-gray-800 mb-4 flex items-center">
                <TrendingUp className="w-5 h-5 mr-2 text-green-600" />
                {t('results.strengths')}
              </h4>
              <p className="text-gray-700 leading-relaxed">
                {t(`types.${primaryType}.strengths`)}
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.8 }}
              className="bg-white rounded-2xl shadow-xl p-6"
            >
              <h4 className="text-xl font-bold text-gray-800 mb-4 flex items-center">
                <MessageCircle className="w-5 h-5 mr-2 text-blue-600" />
                {t('results.communication')}
              </h4>
              <p className="text-gray-700 leading-relaxed">
                {t(`types.${primaryType}.communication`)}
              </p>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.9 }}
              className="bg-white rounded-2xl shadow-xl p-6"
            >
              <h4 className="text-xl font-bold text-gray-800 mb-4 flex items-center">
                <Briefcase className="w-5 h-5 mr-2 text-purple-600" />
                {t('results.workplace')}
              </h4>
              <p className="text-gray-700 leading-relaxed">
                {t(`types.${primaryType}.workplace`)}
              </p>
            </motion.div>
          </div>

          {/* Challenges */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 1.0 }}
            className="bg-amber-50 border-2 border-amber-200 rounded-2xl p-6"
          >
            <h4 className="text-xl font-bold text-amber-800 mb-4">
              {t('results.challenges')}
            </h4>
            <p className="text-amber-700 leading-relaxed mb-4">
              {t(`types.${primaryType}.challenges`)}
            </p>
            
            {/* Recommendations */}
            <div className="mt-4">
              <h5 className="font-semibold text-amber-800 mb-2">{t('results.recommendations')}:</h5>
              <ul className="space-y-1">
                {detailedReport.insights.recommendations.map((rec: string, index: number) => (
                  <li key={index} className="text-amber-700 text-sm">
                    • {t(`results.personalizedRecommendations.${rec}`) || rec}
                  </li>
                ))}
              </ul>
            </div>
          </motion.div>
        </div>

        {/* Action Buttons */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.1 }}
          className="flex flex-wrap justify-center gap-4 mt-8"
        >
          <button
            onClick={generateEnhancedPDF}
            className="flex items-center space-x-2 px-6 py-3 bg-indigo-600 text-white rounded-xl font-medium hover:bg-indigo-700 transition-all duration-200 shadow-lg hover:shadow-xl"
          >
            <Download className="w-5 h-5" />
            <span>{t('common.downloadPDF')}</span>
          </button>
          
          <button
            onClick={shareResults}
            className="flex items-center space-x-2 px-6 py-3 bg-green-600 text-white rounded-xl font-medium hover:bg-green-700 transition-all duration-200 shadow-lg hover:shadow-xl"
          >
            <Share2 className="w-5 h-5" />
            <span>{t('common.shareResults')}</span>
          </button>

          <button
            onClick={exportData}
            className="flex items-center space-x-2 px-6 py-3 bg-purple-600 text-white rounded-xl font-medium hover:bg-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl"
          >
            <BarChart3 className="w-5 h-5" />
            <span>{t('common.exportData')}</span>
          </button>
          
          <button
            onClick={handleRetake}
            className="flex items-center space-x-2 px-6 py-3 bg-gray-600 text-white rounded-xl font-medium hover:bg-gray-700 transition-all duration-200 shadow-lg hover:shadow-xl"
          >
            <RotateCcw className="w-5 h-5" />
            <span>{t('common.retakeTest')}</span>
          </button>
        </motion.div>
      </div>
    </div>
  );
};