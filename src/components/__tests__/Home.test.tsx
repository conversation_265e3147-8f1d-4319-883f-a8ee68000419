import { describe, it, expect, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { HelmetProvider } from 'react-helmet-async';
import { Home } from '../Home';
import { LanguageProvider } from '../../contexts/LanguageContext';

// 模拟子组件
vi.mock('../LanguageSwitcher', () => ({
  LanguageSwitcher: () => <div data-testid="language-switcher">Language Switcher</div>
}));

vi.mock('../TestimonialCarousel', () => ({
  TestimonialCarousel: () => <div data-testid="testimonial-carousel">Testimonials</div>
}));

vi.mock('../FAQSection', () => ({
  FAQSection: () => <div data-testid="faq-section">FAQ</div>
}));

vi.mock('../BlogPreview', () => ({
  BlogPreview: () => <div data-testid="blog-preview">Blog Preview</div>
}));

vi.mock('../StatsCounter', () => ({
  StatsCounter: () => <div data-testid="stats-counter">Stats</div>
}));

vi.mock('../SearchModal', () => ({
  SearchTrigger: () => <div data-testid="search-trigger">Search</div>
}));

// 测试包装器
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <BrowserRouter>
    <HelmetProvider>
      <LanguageProvider>
        {children}
      </LanguageProvider>
    </HelmetProvider>
  </BrowserRouter>
);

describe('Home Component', () => {
  it('renders without crashing', () => {
    render(
      <TestWrapper>
        <Home />
      </TestWrapper>
    );
    
    expect(screen.getByRole('main')).toBeInTheDocument();
  });

  it('displays the main heading', () => {
    render(
      <TestWrapper>
        <Home />
      </TestWrapper>
    );
    
    // 检查是否有主标题
    expect(screen.getByRole('heading', { level: 1 })).toBeInTheDocument();
  });

  it('shows navigation links', () => {
    render(
      <TestWrapper>
        <Home />
      </TestWrapper>
    );
    
    // 检查导航链接
    expect(screen.getByText(/关于/i)).toBeInTheDocument();
    expect(screen.getByText(/博客/i)).toBeInTheDocument();
    expect(screen.getByText(/常见问题/i)).toBeInTheDocument();
    expect(screen.getByText(/联系我们/i)).toBeInTheDocument();
  });

  it('displays the CTA button', () => {
    render(
      <TestWrapper>
        <Home />
      </TestWrapper>
    );
    
    // 检查主要行动按钮
    const ctaButton = screen.getByRole('button', { name: /开始免费评估/i });
    expect(ctaButton).toBeInTheDocument();
  });

  it('handles CTA button click', async () => {
    const mockNavigate = vi.fn();
    vi.mock('react-router-dom', async () => {
      const actual = await vi.importActual('react-router-dom');
      return {
        ...actual,
        useNavigate: () => mockNavigate
      };
    });

    render(
      <TestWrapper>
        <Home />
      </TestWrapper>
    );
    
    const ctaButton = screen.getByRole('button', { name: /开始免费评估/i });
    fireEvent.click(ctaButton);
    
    // 验证导航是否被调用
    await waitFor(() => {
      expect(mockNavigate).toHaveBeenCalledWith('/assessment');
    });
  });

  it('renders all child components', () => {
    render(
      <TestWrapper>
        <Home />
      </TestWrapper>
    );
    
    // 检查所有子组件是否渲染
    expect(screen.getByTestId('language-switcher')).toBeInTheDocument();
    expect(screen.getByTestId('search-trigger')).toBeInTheDocument();
    expect(screen.getByTestId('testimonial-carousel')).toBeInTheDocument();
    expect(screen.getByTestId('faq-section')).toBeInTheDocument();
    expect(screen.getByTestId('blog-preview')).toBeInTheDocument();
    expect(screen.getByTestId('stats-counter')).toBeInTheDocument();
  });

  it('displays DISC type cards', () => {
    render(
      <TestWrapper>
        <Home />
      </TestWrapper>
    );
    
    // 检查DISC类型卡片
    expect(screen.getByText(/支配型/i)).toBeInTheDocument();
    expect(screen.getByText(/影响型/i)).toBeInTheDocument();
    expect(screen.getByText(/稳定型/i)).toBeInTheDocument();
    expect(screen.getByText(/谨慎型/i)).toBeInTheDocument();
  });

  it('shows features section', () => {
    render(
      <TestWrapper>
        <Home />
      </TestWrapper>
    );
    
    // 检查功能特性部分
    expect(screen.getByText(/科学可靠/i)).toBeInTheDocument();
    expect(screen.getByText(/简单易用/i)).toBeInTheDocument();
    expect(screen.getByText(/详细报告/i)).toBeInTheDocument();
  });

  it('displays footer information', () => {
    render(
      <TestWrapper>
        <Home />
      </TestWrapper>
    );
    
    // 检查页脚信息
    expect(screen.getByText(/隐私政策/i)).toBeInTheDocument();
    expect(screen.getByText(/服务条款/i)).toBeInTheDocument();
  });

  it('is accessible', async () => {
    const { container } = render(
      <TestWrapper>
        <Home />
      </TestWrapper>
    );
    
    // 检查基本的可访问性
    expect(container.querySelector('main')).toHaveAttribute('role', 'main');
    
    // 检查是否有跳转链接
    const skipLink = container.querySelector('a[href="#main-content"]');
    expect(skipLink).toBeInTheDocument();
  });

  it('handles responsive design', () => {
    // 模拟移动设备
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 375,
    });
    
    render(
      <TestWrapper>
        <Home />
      </TestWrapper>
    );
    
    // 在移动设备上应该仍然能正常渲染
    expect(screen.getByRole('main')).toBeInTheDocument();
  });
});
