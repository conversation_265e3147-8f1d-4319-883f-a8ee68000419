import React from 'react';
import { Globe } from 'lucide-react';
import { useLanguage } from '../contexts/LanguageContext';
import { Language } from '../types';
import { trackLanguageChange } from './GoogleAnalytics';

const languages = [
  { code: 'en' as Language, name: 'English', nativeName: 'English' },
  { code: 'es' as Language, name: 'Español', nativeName: 'Español' },
  { code: 'zh' as Language, name: '简体中文', nativeName: '简体中文' }
];

export const LanguageSwitcher: React.FC = () => {
  const { language, setLanguage, t } = useLanguage();

  const handleLanguageChange = (newLanguage: Language) => {
    if (newLanguage !== language) {
      trackLanguageChange(newLanguage);
      setLanguage(newLanguage);
    }
  };

  return (
    <div className="relative group">
      <button
        className="flex items-center space-x-2 px-3 py-2 rounded-lg bg-white/10 backdrop-blur-sm border border-white/20 hover:bg-white/20 transition-all duration-200"
        aria-label={t('common.language')}
      >
        <Globe className="w-4 h-4" />
        <span className="text-sm font-medium">
          {languages.find(lang => lang.code === language)?.nativeName}
        </span>
      </button>
      
      <div className="absolute right-0 top-full mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
        {languages.map((lang) => (
          <button
            key={lang.code}
            onClick={() => handleLanguageChange(lang.code)}
            className={`w-full px-4 py-2 text-left hover:bg-gray-50 flex items-center space-x-3 first:rounded-t-lg last:rounded-b-lg transition-colors duration-150 ${
              language === lang.code ? 'bg-indigo-50 text-indigo-600' : 'text-gray-700'
            }`}
          >
            <span className="font-medium">{lang.nativeName}</span>
            {language === lang.code && (
              <span className="ml-auto text-indigo-600">✓</span>
            )}
          </button>
        ))}
      </div>
    </div>
  );
};