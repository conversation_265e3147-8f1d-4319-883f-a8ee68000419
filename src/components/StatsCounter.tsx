import React, { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { Users, Award, Globe, TrendingUp } from 'lucide-react';
import { useLanguage } from '../contexts/LanguageContext';

interface Stat {
  icon: React.ElementType;
  value: number;
  labelKey: string;
  suffix: string;
  color: string;
}

const stats: Stat[] = [
  {
    icon: Users,
    value: 50000,
    labelKey: "assessmentsCompleted",
    suffix: "+",
    color: "text-blue-600"
  },
  {
    icon: Award,
    value: 98,
    labelKey: "accuracyRate",
    suffix: "%",
    color: "text-green-600"
  },
  {
    icon: Globe,
    value: 25,
    labelKey: "countriesServed",
    suffix: "+",
    color: "text-purple-600"
  },
  {
    icon: TrendingUp,
    value: 95,
    labelKey: "userSatisfaction",
    suffix: "%",
    color: "text-orange-600"
  }
];

const useCountUp = (end: number, duration: number = 2000) => {
  const [count, setCount] = useState(0);
  const [isVisible, setIsVisible] = useState(false);
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.1 }
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => observer.disconnect();
  }, []);

  useEffect(() => {
    if (!isVisible) return;

    let startTime: number;
    const animate = (currentTime: number) => {
      if (!startTime) startTime = currentTime;
      const progress = Math.min((currentTime - startTime) / duration, 1);
      
      setCount(Math.floor(progress * end));
      
      if (progress < 1) {
        requestAnimationFrame(animate);
      }
    };
    
    requestAnimationFrame(animate);
  }, [end, duration, isVisible]);

  return { count, ref };
};

export const StatsCounter: React.FC = () => {
  const { t } = useLanguage();

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.8, delay: 0.4 }}
      className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto mb-16"
    >
      {stats.map((stat, index) => {
        const { count, ref } = useCountUp(stat.value);

        return (
          <div
            key={index}
            ref={ref}
            className="text-center"
          >
            <div className={`inline-flex items-center justify-center w-12 h-12 rounded-full bg-gray-100 ${stat.color} mb-3`}>
              <stat.icon className="w-6 h-6" />
            </div>
            <div className="text-3xl font-bold text-gray-800 mb-1">
              {count.toLocaleString()}{stat.suffix}
            </div>
            <div className="text-sm text-gray-600">
              {t(`home.stats.${stat.labelKey}`)}
            </div>
          </div>
        );
      })}
    </motion.div>
  );
};