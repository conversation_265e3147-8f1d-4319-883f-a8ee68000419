import React from 'react';
import { motion } from 'framer-motion';
import { Shield, AlertTriangle, X } from 'lucide-react';
import { useLanguage } from '../contexts/LanguageContext';

interface SecurityWarningProps {
  warningCount: number;
  isVisible: boolean;
  onDismiss: () => void;
  onTerminate: () => void;
}

export const SecurityWarning: React.FC<SecurityWarningProps> = ({
  warningCount,
  isVisible,
  onDismiss,
  onTerminate
}) => {
  const { t } = useLanguage();

  if (!isVisible) return null;

  const isTerminal = warningCount >= 5; // 改为5次才终止

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
    >
      <motion.div
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        className={`bg-white rounded-2xl shadow-2xl p-6 max-w-md w-full ${
          isTerminal ? 'border-2 border-red-500' : 'border-2 border-amber-500'
        }`}
      >
        <div className="flex items-center space-x-3 mb-4">
          {isTerminal ? (
            <AlertTriangle className="w-8 h-8 text-red-500" />
          ) : (
            <Shield className="w-8 h-8 text-amber-500" />
          )}
          <h3 className={`text-xl font-bold ${
            isTerminal ? 'text-red-700' : 'text-amber-700'
          }`}>
            {isTerminal ? t('assessment.security.terminated') : t('assessment.security.notice')}
          </h3>
          {!isTerminal && (
            <button
              onClick={onDismiss}
              className="ml-auto p-1 hover:bg-gray-100 rounded-full"
            >
              <X className="w-5 h-5 text-gray-500" />
            </button>
          )}
        </div>

        <div className="mb-6">
          <p className="text-gray-700 mb-3">
            {isTerminal
              ? t('assessment.security.terminatedMessage')
              : t('assessment.security.unusualActivity').replace('{{count}}', warningCount.toString())
            }
          </p>

          {!isTerminal && (
            <ul className="text-sm text-gray-600 space-y-1 ml-4">
              <li>• {t('assessment.security.guidelines.stayFocused')}</li>
              <li>• {t('assessment.security.guidelines.avoidSwitching')}</li>
              <li>• {t('assessment.security.guidelines.noDevTools')}</li>
              <li>• {t('assessment.security.guidelines.beHonest')}</li>
            </ul>
          )}
        </div>

        <div className="flex space-x-3">
          {isTerminal ? (
            <button
              onClick={onTerminate}
              className="flex-1 px-4 py-2 bg-red-600 text-white rounded-lg font-medium hover:bg-red-700 transition-colors"
            >
              {t('assessment.security.actions.returnHome')}
            </button>
          ) : (
            <>
              <button
                onClick={onDismiss}
                className="flex-1 px-4 py-2 bg-amber-600 text-white rounded-lg font-medium hover:bg-amber-700 transition-colors"
              >
                {t('assessment.security.actions.continueAssessment')}
              </button>
              <button
                onClick={onTerminate}
                className="px-4 py-2 bg-gray-300 text-gray-700 rounded-lg font-medium hover:bg-gray-400 transition-colors"
              >
                {t('assessment.security.actions.exit')}
              </button>
            </>
          )}
        </div>
      </motion.div>
    </motion.div>
  );
};