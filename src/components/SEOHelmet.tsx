import React from 'react';
import { Helmet } from 'react-helmet-async';
import { useLanguage } from '../contexts/LanguageContext';
import { StructuredData, HrefLang } from './StructuredData';

interface SEOHelmetProps {
  title?: string;
  description?: string;
  keywords?: string;
  image?: string;
  url?: string;
  type?: string;
  publishedTime?: string;
  modifiedTime?: string;
  author?: string;
  section?: string;
  structuredDataType?: 'website' | 'article' | 'faq' | 'organization' | 'service';
  structuredData?: any;
  noIndex?: boolean;
  canonical?: string;
}

export const SEOHelmet: React.FC<SEOHelmetProps> = ({
  title,
  description,
  keywords,
  image = '/og-image.jpg',
  url = window.location.href,
  type = 'website',
  publishedTime,
  modifiedTime,
  author,
  section,
  structuredDataType,
  structuredData,
  noIndex = false,
  canonical
}) => {
  const { language, t } = useLanguage();
  
  const defaultTitle = t('home.title');
  const defaultDescription = t('home.description');
  const pageTitle = title ? `${title} | ${defaultTitle}` : defaultTitle;
  const siteName = 'DISC Traits';

  const defaultStructuredData = {
    "@context": "https://schema.org",
    "@graph": [
      {
        "@type": "WebSite",
        "@id": `${window.location.origin}/#website`,
        "url": window.location.origin,
        "name": siteName,
        "description": defaultDescription,
        "potentialAction": [
          {
            "@type": "SearchAction",
            "target": {
              "@type": "EntryPoint",
              "urlTemplate": `${window.location.origin}/search?q={search_term_string}`
            },
            "query-input": "required name=search_term_string"
          }
        ],
        "inLanguage": language
      },
      {
        "@type": "WebApplication",
        "@id": `${url}#webapp`,
        "url": url,
        "name": pageTitle,
        "description": description || defaultDescription,
        "applicationCategory": "UtilityApplication",
        "operatingSystem": "Web",
        "browserRequirements": "Requires JavaScript. Requires HTML5.",
        "softwareVersion": "1.0",
        "offers": {
          "@type": "Offer",
          "price": "0",
          "priceCurrency": "USD",
          "availability": "https://schema.org/InStock"
        },
        "author": {
          "@type": "Organization",
          "name": siteName,
          "url": window.location.origin
        },
        "aggregateRating": {
          "@type": "AggregateRating",
          "ratingValue": "4.8",
          "reviewCount": "1247",
          "bestRating": "5",
          "worstRating": "1"
        },
        "inLanguage": language
      },
      {
        "@type": "Organization",
        "@id": `${window.location.origin}/#organization`,
        "name": siteName,
        "url": window.location.origin,
        "logo": {
          "@type": "ImageObject",
          "url": `${window.location.origin}/logo.png`,
          "width": 512,
          "height": 512
        },
        "sameAs": [
          "https://www.facebook.com/disctraits",
          "https://www.twitter.com/disctraits",
          "https://www.linkedin.com/company/disctraits"
        ],
        "contactPoint": {
          "@type": "ContactPoint",
          "telephone": "******-0123",
          "contactType": "customer service",
          "availableLanguage": ["English", "Spanish", "Chinese"]
        }
      },
      {
        "@type": "BreadcrumbList",
        "@id": `${url}#breadcrumb`,
        "itemListElement": [
          {
            "@type": "ListItem",
            "position": 1,
            "name": "Home",
            "item": window.location.origin
          }
        ]
      }
    ]
  };

  // Add FAQ structured data if we're on a page with FAQs
  if (url.includes('faq') || !title) {
    defaultStructuredData["@graph"].push({
      "@type": "FAQPage",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "What is the DISC personality assessment?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "DISC is a behavioral assessment tool that measures four primary personality traits: Dominance (D), Influence (I), Steadiness (S), and Conscientiousness (C). It helps individuals understand their behavioral style, communication preferences, and work approach."
          }
        },
        {
          "@type": "Question",
          "name": "How long does the assessment take?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "The DISC assessment typically takes 5-10 minutes to complete. It consists of 24 carefully crafted questions designed to accurately identify your behavioral preferences and communication style."
          }
        },
        {
          "@type": "Question",
          "name": "Is the assessment scientifically validated?",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "Yes, our DISC assessment is based on decades of psychological research and has been validated through extensive testing. It's used by Fortune 500 companies, HR professionals, and career coaches worldwide."
          }
        }
      ]
    });
  }

  return (
    <>
      <Helmet>
        {/* Basic Meta Tags */}
        <html lang={language} />
        <title>{pageTitle}</title>
        <meta name="description" content={description || defaultDescription} />
        {keywords && <meta name="keywords" content={keywords} />}
        <meta name="author" content={author || siteName} />
        <meta name="robots" content={noIndex ? "noindex, nofollow" : "index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1"} />
        <meta name="googlebot" content={noIndex ? "noindex, nofollow" : "index, follow"} />
      
      {/* Canonical URL */}
      <link rel="canonical" href={canonical || url} />
      
      {/* Language alternates */}
      <link rel="alternate" hrefLang="en" href={url.replace(/\/(es|zh)\//, '/en/')} />
      <link rel="alternate" hrefLang="es" href={url.replace(/\/(en|zh)\//, '/es/')} />
      <link rel="alternate" hrefLang="zh" href={url.replace(/\/(en|es)\//, '/zh/')} />
      <link rel="alternate" hrefLang="x-default" href={url.replace(/\/(es|zh)\//, '/en/')} />
      
      {/* Open Graph */}
      <meta property="og:type" content={type} />
      <meta property="og:title" content={pageTitle} />
      <meta property="og:description" content={description || defaultDescription} />
      <meta property="og:image" content={image} />
      <meta property="og:image:width" content="1200" />
      <meta property="og:image:height" content="630" />
      <meta property="og:image:alt" content={pageTitle} />
      <meta property="og:url" content={url} />
      <meta property="og:site_name" content={siteName} />
      <meta property="og:locale" content={language === 'en' ? 'en_US' : language === 'es' ? 'es_ES' : 'zh_CN'} />
      {publishedTime && <meta property="article:published_time" content={publishedTime} />}
      {modifiedTime && <meta property="article:modified_time" content={modifiedTime} />}
      {author && <meta property="article:author" content={author} />}
      {section && <meta property="article:section" content={section} />}
      
      {/* Twitter Card */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:site" content="@disctraits" />
      <meta name="twitter:creator" content="@disctraits" />
      <meta name="twitter:title" content={pageTitle} />
      <meta name="twitter:description" content={description || defaultDescription} />
      <meta name="twitter:image" content={image} />
      <meta name="twitter:image:alt" content={pageTitle} />
      
      {/* Additional SEO Meta Tags */}
      <meta name="theme-color" content="#4F46E5" />
      <meta name="msapplication-TileColor" content="#4F46E5" />
      <meta name="apple-mobile-web-app-capable" content="yes" />
      <meta name="apple-mobile-web-app-status-bar-style" content="default" />
      <meta name="apple-mobile-web-app-title" content="DISC Traits" />
      <meta name="format-detection" content="telephone=no" />
      
      {/* Preconnect to external domains */}
      <link rel="preconnect" href="https://fonts.googleapis.com" />
      <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
      <link rel="preconnect" href="https://images.pexels.com" />
      
      {/* DNS Prefetch */}
      <link rel="dns-prefetch" href="//fonts.googleapis.com" />
      <link rel="dns-prefetch" href="//fonts.gstatic.com" />
      <link rel="dns-prefetch" href="//images.pexels.com" />
      
      {/* Structured Data */}
      <script type="application/ld+json">
        {JSON.stringify(defaultStructuredData)}
      </script>
      
      {/* Additional Performance Hints */}
      <link rel="preload" href="/fonts/inter-var.woff2" as="font" type="font/woff2" crossOrigin="anonymous" />
      
      {/* Security Headers */}
      <meta httpEquiv="X-Content-Type-Options" content="nosniff" />
      <meta httpEquiv="X-Frame-Options" content="DENY" />
      <meta httpEquiv="X-XSS-Protection" content="1; mode=block" />
      <meta httpEquiv="Referrer-Policy" content="strict-origin-when-cross-origin" />
      
      {/* Verification Tags (add your actual verification codes) */}
      <meta name="google-site-verification" content="your-google-verification-code" />
      <meta name="msvalidate.01" content="your-bing-verification-code" />
      <meta name="yandex-verification" content="your-yandex-verification-code" />
    </Helmet>

    {/* Additional Structured Data */}
    {structuredDataType && (
      <StructuredData type={structuredDataType} data={structuredData} />
    )}

    {/* Hreflang tags for multilingual SEO */}
    <HrefLang />
  </>
  );
};