/* 可访问性增强样式 */

/* 高对比度模式 */
.high-contrast {
  --bg-primary: #000000;
  --bg-secondary: #ffffff;
  --text-primary: #ffffff;
  --text-secondary: #000000;
  --border-color: #ffffff;
  --link-color: #ffff00;
  --button-bg: #ffffff;
  --button-text: #000000;
}

.high-contrast * {
  background-color: var(--bg-primary) !important;
  color: var(--text-primary) !important;
  border-color: var(--border-color) !important;
}

.high-contrast a {
  color: var(--link-color) !important;
  text-decoration: underline !important;
}

.high-contrast button,
.high-contrast input,
.high-contrast select,
.high-contrast textarea {
  background-color: var(--button-bg) !important;
  color: var(--button-text) !important;
  border: 2px solid var(--border-color) !important;
}

.high-contrast img {
  filter: contrast(150%) brightness(150%);
}

/* 大字体模式 */
.large-text {
  font-size: 120% !important;
}

.large-text * {
  font-size: inherit !important;
  line-height: 1.6 !important;
}

.large-text h1 {
  font-size: 2.5rem !important;
}

.large-text h2 {
  font-size: 2rem !important;
}

.large-text h3 {
  font-size: 1.75rem !important;
}

.large-text h4 {
  font-size: 1.5rem !important;
}

.large-text h5 {
  font-size: 1.25rem !important;
}

.large-text h6 {
  font-size: 1.125rem !important;
}

.large-text button,
.large-text input,
.large-text select,
.large-text textarea {
  font-size: 1.125rem !important;
  padding: 0.75rem 1rem !important;
}

/* 减少动画模式 */
.reduced-motion *,
.reduced-motion *::before,
.reduced-motion *::after {
  animation-duration: 0.01ms !important;
  animation-iteration-count: 1 !important;
  transition-duration: 0.01ms !important;
  scroll-behavior: auto !important;
}

.reduced-motion .animate-spin {
  animation: none !important;
}

.reduced-motion .animate-pulse {
  animation: none !important;
}

.reduced-motion .animate-bounce {
  animation: none !important;
}

/* 键盘导航增强 */
.keyboard-navigation *:focus {
  outline: 3px solid #4F46E5 !important;
  outline-offset: 2px !important;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.3) !important;
}

.keyboard-navigation button:focus,
.keyboard-navigation a:focus,
.keyboard-navigation input:focus,
.keyboard-navigation select:focus,
.keyboard-navigation textarea:focus {
  outline: 3px solid #4F46E5 !important;
  outline-offset: 2px !important;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.3) !important;
}

/* 焦点指示器 */
.focus-indicators *:focus-visible {
  outline: 3px solid #4F46E5 !important;
  outline-offset: 2px !important;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.3) !important;
}

.focus-indicators button:focus-visible,
.focus-indicators a:focus-visible {
  background-color: rgba(79, 70, 229, 0.1) !important;
}

/* 跳转链接 */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: #4F46E5;
  color: white;
  padding: 8px;
  text-decoration: none;
  border-radius: 4px;
  z-index: 1000;
  font-weight: bold;
}

.skip-link:focus {
  top: 6px;
}

/* 屏幕阅读器专用内容 */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.sr-only-focusable:focus {
  position: static;
  width: auto;
  height: auto;
  padding: inherit;
  margin: inherit;
  overflow: visible;
  clip: auto;
  white-space: normal;
}

/* 改进的按钮样式 */
.accessible-button {
  min-height: 44px;
  min-width: 44px;
  padding: 12px 16px;
  border: 2px solid transparent;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.accessible-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.accessible-button:active {
  transform: translateY(0);
}

.accessible-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* 改进的表单样式 */
.accessible-input {
  min-height: 44px;
  padding: 12px 16px;
  border: 2px solid #d1d5db;
  border-radius: 6px;
  font-size: 16px;
  transition: border-color 0.2s ease;
}

.accessible-input:focus {
  border-color: #4F46E5;
  outline: none;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

.accessible-input:invalid {
  border-color: #ef4444;
}

.accessible-input:invalid:focus {
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

/* 改进的链接样式 */
.accessible-link {
  color: #4F46E5;
  text-decoration: underline;
  text-underline-offset: 2px;
  transition: color 0.2s ease;
}

.accessible-link:hover {
  color: #3730a3;
  text-decoration-thickness: 2px;
}

.accessible-link:focus {
  outline: 2px solid #4F46E5;
  outline-offset: 2px;
  border-radius: 2px;
}

/* 高对比度模式下的特殊处理 */
@media (prefers-contrast: high) {
  * {
    border-width: 2px !important;
  }
  
  button, input, select, textarea {
    border: 2px solid currentColor !important;
  }
}

/* 减少动画偏好 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* 深色模式偏好 */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-primary: #1f2937;
    --bg-secondary: #374151;
    --text-primary: #f9fafb;
    --text-secondary: #d1d5db;
  }
}

/* 打印样式 */
@media print {
  .accessibility-panel,
  .skip-link,
  button[aria-label*="可访问性"] {
    display: none !important;
  }
  
  * {
    background: white !important;
    color: black !important;
    box-shadow: none !important;
  }
  
  a {
    text-decoration: underline !important;
  }
  
  a[href^="http"]:after {
    content: " (" attr(href) ")";
    font-size: 0.8em;
  }
}
