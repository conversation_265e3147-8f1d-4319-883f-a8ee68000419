// 简单的数据加密工具
export const encryptData = (data: any): string => {
  const jsonString = JSON.stringify(data);
  const encoded = btoa(jsonString);
  // 简单的字符替换加密
  return encoded.split('').map(char => 
    String.fromCharCode(char.charCodeAt(0) + 3)
  ).join('');
};

export const decryptData = (encryptedData: string): any => {
  try {
    const decoded = encryptedData.split('').map(char => 
      String.fromCharCode(char.charCodeAt(0) - 3)
    ).join('');
    const jsonString = atob(decoded);
    return JSON.parse(jsonString);
  } catch (error) {
    console.error('Decryption failed:', error);
    return null;
  }
};

export const generateSessionId = (): string => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
};

export const getDeviceFingerprint = (): string => {
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  if (ctx) {
    ctx.textBaseline = 'top';
    ctx.font = '14px Arial';
    ctx.fillText('Device fingerprint', 2, 2);
  }
  
  return btoa(JSON.stringify({
    userAgent: navigator.userAgent,
    language: navigator.language,
    platform: navigator.platform,
    screen: `${screen.width}x${screen.height}`,
    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    canvas: canvas.toDataURL()
  }));
};