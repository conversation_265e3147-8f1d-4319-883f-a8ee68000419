import { UserResponse, AssessmentResult } from '../types';

export interface AnalyticsData {
  totalTime: number;
  averageTimePerQuestion: number;
  quickestResponse: number;
  slowestResponse: number;
  responseTimeDistribution: {
    fast: number; // < 5s
    normal: number; // 5-15s
    slow: number; // > 15s
  };
  consistencyScore: number;
  confidenceLevel: 'high' | 'medium' | 'low';
}

export const calculateAnalytics = (
  responses: UserResponse[],
  questionTimes: number[]
): AnalyticsData => {
  // 边界检查：如果没有时间数据，返回默认值
  if (!questionTimes || questionTimes.length === 0) {
    return {
      totalTime: 0,
      averageTimePerQuestion: 0,
      quickestResponse: 0,
      slowestResponse: 0,
      responseTimeDistribution: { fast: 0, normal: 0, slow: 0 },
      consistencyScore: 0,
      confidenceLevel: 'low'
    };
  }

  const totalTime = questionTimes.reduce((sum, time) => sum + time, 0);
  const averageTimePerQuestion = totalTime / questionTimes.length;
  const quickestResponse = Math.min(...questionTimes);
  const slowestResponse = Math.max(...questionTimes);

  // 响应时间分布
  const responseTimeDistribution = questionTimes.reduce(
    (dist, time) => {
      if (time < 5) dist.fast++;
      else if (time <= 15) dist.normal++;
      else dist.slow++;
      return dist;
    },
    { fast: 0, normal: 0, slow: 0 }
  );

  // 一致性评分（基于响应时间的标准差）
  let consistencyScore = 0;
  if (averageTimePerQuestion > 0 && questionTimes.length > 1) {
    const timeVariance = questionTimes.reduce((sum, time) => {
      return sum + Math.pow(time - averageTimePerQuestion, 2);
    }, 0) / questionTimes.length;
    const timeStdDev = Math.sqrt(timeVariance);
    consistencyScore = Math.max(0, 100 - (timeStdDev / averageTimePerQuestion) * 100);
  } else if (questionTimes.length === 1) {
    consistencyScore = 100; // 只有一个数据点，认为是完全一致的
  }

  // 置信度评估
  let confidenceLevel: 'high' | 'medium' | 'low' = 'high';
  if (responseTimeDistribution.fast > questionTimes.length * 0.5) {
    confidenceLevel = 'low'; // 太多快速回答
  } else if (consistencyScore < 60) {
    confidenceLevel = 'medium'; // 回答时间不一致
  }

  return {
    totalTime,
    averageTimePerQuestion: Math.round(averageTimePerQuestion * 10) / 10,
    quickestResponse,
    slowestResponse,
    responseTimeDistribution,
    consistencyScore: Math.round(consistencyScore) || 0,
    confidenceLevel
  };
};

export const generateDetailedReport = (
  result: AssessmentResult,
  analytics: AnalyticsData
): any => {
  return {
    assessment: {
      id: result.id,
      completedAt: result.completedAt,
      language: result.language,
      primaryType: result.primaryType,
      scores: result.scores,
      percentages: result.percentages
    },
    performance: {
      totalTime: analytics.totalTime,
      averageTimePerQuestion: analytics.averageTimePerQuestion,
      responseTimeRange: {
        fastest: analytics.quickestResponse,
        slowest: analytics.slowestResponse
      },
      distribution: analytics.responseTimeDistribution,
      consistencyScore: analytics.consistencyScore,
      confidenceLevel: analytics.confidenceLevel
    },
    insights: {
      responsePattern: analytics.confidenceLevel === 'high' ? 'Consistent and thoughtful responses' : 
                     analytics.confidenceLevel === 'medium' ? 'Moderate response consistency' : 
                     'Rapid responses detected - results may need verification',
      recommendations: generateRecommendations(result.primaryType, analytics)
    }
  };
};

const generateRecommendations = (primaryType: string, analytics: AnalyticsData): string[] => {
  const baseRecommendations = {
    D: ['patience_listening', 'collaborative_decision'],
    I: ['attention_detail', 'follow_through'],
    S: ['change_management', 'assertiveness'],
    C: ['flexibility_adaptability', 'quick_decision']
  };

  const recommendations = [...(baseRecommendations[primaryType as keyof typeof baseRecommendations] || [])];

  if (analytics.confidenceLevel === 'low') {
    recommendations.push('retake_assessment');
  }

  return recommendations;
};