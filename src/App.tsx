import React from 'react';
import { BrowserRouter as Router, Routes, Route, useNavigate, Link } from 'react-router-dom';
import { HelmetProvider } from 'react-helmet-async';
import { LanguageProvider } from './contexts/LanguageContext';
import { Home } from './components/Home';
import { Assessment } from './components/Assessment';
import { Results } from './components/Results';
import { AboutPage } from './pages/AboutPage';
import { BlogPage } from './pages/BlogPage';
import { BlogPostPage } from './pages/BlogPostPage';
import { ContactPage } from './pages/ContactPage';
import { FAQPage } from './pages/FAQPage';
import { PrivacyPage } from './pages/PrivacyPage';
import { TermsPage } from './pages/TermsPage';
import { GoogleAnalytics } from './components/GoogleAnalytics';
import { PerformanceOptimizer } from './components/PerformanceOptimizer';
import { AccessibilityEnhancer } from './components/AccessibilityEnhancer';
import { FeedbackWidget } from './components/FeedbackWidget';
import { SecurityEnhancer } from './components/SecurityEnhancer';
import { ErrorBoundary } from './components/ErrorBoundary';
import { ServiceWorkerManager } from './components/ServiceWorkerManager';
import { DevDashboard } from './components/DevDashboard';
import { AssessmentResult } from './types';

// 创建一个全局状态来存储评估结果
const AssessmentResultProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [assessmentResult, setAssessmentResult] = React.useState<AssessmentResult | null>(null);

  React.useEffect(() => {
    // 从localStorage恢复评估结果
    const savedResult = localStorage.getItem('disc-assessment-result');
    if (savedResult) {
      try {
        setAssessmentResult(JSON.parse(savedResult));
      } catch (error) {
        console.error('Failed to parse saved assessment result:', error);
      }
    }
  }, []);

  const saveResult = (result: AssessmentResult) => {
    setAssessmentResult(result);
    localStorage.setItem('disc-assessment-result', JSON.stringify(result));
  };

  const clearResult = () => {
    setAssessmentResult(null);
    localStorage.removeItem('disc-assessment-result');
  };

  return (
    <AssessmentResultContext.Provider value={{ assessmentResult, saveResult, clearResult }}>
      {children}
    </AssessmentResultContext.Provider>
  );
};

// 创建Context
const AssessmentResultContext = React.createContext<{
  assessmentResult: AssessmentResult | null;
  saveResult: (result: AssessmentResult) => void;
  clearResult: () => void;
}>({
  assessmentResult: null,
  saveResult: () => {},
  clearResult: () => {}
});

export const useAssessmentResult = () => React.useContext(AssessmentResultContext);

function App() {
  return (
    <HelmetProvider>
      <LanguageProvider>
        <AssessmentResultProvider>
          <ErrorBoundary>
            <Router>
            <GoogleAnalytics />
            <PerformanceOptimizer />
            <SecurityEnhancer />
            <AccessibilityEnhancer />
            <FeedbackWidget />
            <ServiceWorkerManager />
            <DevDashboard />
            <div className="min-h-screen">
              <Routes>
                <Route path="/" element={<Home />} />
                <Route path="/assessment" element={<AssessmentWrapper />} />
                <Route path="/results" element={<ResultsWrapper />} />
                <Route path="/about" element={<AboutPage />} />
                <Route path="/blog" element={<BlogPage />} />
                <Route path="/blog/:slug" element={<BlogPostPage />} />
                <Route path="/contact" element={<ContactPage />} />
                <Route path="/faq" element={<FAQPage />} />
                <Route path="/privacy" element={<PrivacyPage />} />
                <Route path="/terms" element={<TermsPage />} />
                {/* 404 页面 */}
                <Route path="*" element={<NotFoundPage />} />
              </Routes>
            </div>
          </Router>
          </ErrorBoundary>
        </AssessmentResultProvider>
      </LanguageProvider>
    </HelmetProvider>
  );
}

// 包装组件来处理评估流程
const AssessmentWrapper: React.FC = () => {
  const { saveResult } = useAssessmentResult();
  const navigate = useNavigate();

  const handleComplete = (result: AssessmentResult) => {
    saveResult(result);
    navigate('/results');
  };

  const handleBackToHome = () => {
    navigate('/');
  };

  return (
    <Assessment
      onComplete={handleComplete}
      onBackToHome={handleBackToHome}
    />
  );
};

const ResultsWrapper: React.FC = () => {
  const { assessmentResult, clearResult } = useAssessmentResult();
  const navigate = useNavigate();

  React.useEffect(() => {
    if (!assessmentResult) {
      navigate('/');
    }
  }, [assessmentResult, navigate]);

  const handleRetake = () => {
    clearResult();
    navigate('/assessment');
  };

  const handleBackToHome = () => {
    navigate('/');
  };

  if (!assessmentResult) {
    return null;
  }

  return (
    <Results
      result={assessmentResult}
      onRetake={handleRetake}
      onBackToHome={handleBackToHome}
    />
  );
};

// 404页面组件
const NotFoundPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-indigo-50 via-white to-purple-50 flex items-center justify-center">
      <div className="text-center">
        <h1 className="text-6xl font-bold text-gray-800 mb-4">404</h1>
        <h2 className="text-2xl font-semibold text-gray-600 mb-4">页面未找到</h2>
        <p className="text-gray-500 mb-8">抱歉，您访问的页面不存在。</p>
        <Link
          to="/"
          className="inline-flex items-center px-6 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors"
        >
          返回首页
        </Link>
      </div>
    </div>
  );
};

export default App;